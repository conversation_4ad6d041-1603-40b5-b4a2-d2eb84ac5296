import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./App.css";
import Home from "./components/Home";
import LoginPage from "./components/LoginPage";
import SignIn from "./components/SignIn";
import ListingsPage from "./components/ListingsPage";
import VerifyEmail from "./components/VerifyEmail";
import GuestRoute from "./components/GuestRoute";
import PrivateRoute from "./components/PrivateRoute";
import AgentRoute from "./components/AgentRoute.jsx";
import { AuthProvider } from "./context/AuthProvider";
import ProfilePage from "./components/ProfilePage";
import ListPropertyPage from "./components/ListPropertyPage";
import MyListings from "./components/MyListings";
import PropertyDetail from "./components/PropertyDetail.jsx";
import SchedulePage from "./components/SchedulePage.jsx";
import ScheduleAppointment from "./components/ScheduleAppointment.jsx";
import PropertyViewings from "./components/PropertyViewings.jsx";
import Calendar from "./components/Calendar.jsx";

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          <Route path="/" element={<Home />} />

          <Route
            path="/login"
            element={
              <GuestRoute>
                <LoginPage />
              </GuestRoute>
            }
          />
          <Route
            path="/createAccount"
            element={
              <GuestRoute>
                <SignIn />
              </GuestRoute>
            }
          />
          <Route path="/verify" element={<VerifyEmail />} />
          <Route path="/search" element={<ListingsPage />} />

          {/* Protected Routes */}
          <Route
            path="/profile"
            element={
              <PrivateRoute>
                <ProfilePage />
              </PrivateRoute>
            }
          />
          <Route
            path="/listproperties"
            element={
              <AgentRoute>
                <ListPropertyPage />
              </AgentRoute>
            }
          />
          <Route
            path="/myListings"
            element={
              <AgentRoute>
                <MyListings />
              </AgentRoute>
            }
          />
          <Route
            path="/schedule/:id"
            element={
              <AgentRoute>
                <SchedulePage />
              </AgentRoute>
            }
          />
          <Route
            path="/mylistings/viewings/:propertyId"
            element={
              <AgentRoute>
                <PropertyViewings />
              </AgentRoute>
            }
          />
          <Route
            path="/calendar"
            element={
              <PrivateRoute>
                <Calendar />
              </PrivateRoute>
            }
          />
          <Route
            path="/property/:id"
            element={
              <PrivateRoute>
                <PropertyDetail />
              </PrivateRoute>
            }
          />
          <Route
            path="/scheduleAppointMent/:propertyId"
            element={
              <PrivateRoute>
                <ScheduleAppointment />
              </PrivateRoute>
            }
          />
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;
