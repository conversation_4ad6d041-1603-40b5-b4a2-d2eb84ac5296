import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import "./App.css";
import Home from "./components/Home";
import { AuthProvider } from "./context/AuthProvider";

// Lazy load components for better performance
const LoginPage = lazy(() => import("./components/LoginPage"));
const SignIn = lazy(() => import("./components/SignIn"));
const ListingsPage = lazy(() => import("./components/ListingsPage"));
const VerifyEmail = lazy(() => import("./components/VerifyEmail"));
const GuestRoute = lazy(() => import("./components/GuestRoute"));
const PrivateRoute = lazy(() => import("./components/PrivateRoute"));
const AgentRoute = lazy(() => import("./components/AgentRoute.jsx"));
const ProfilePage = lazy(() => import("./components/ProfilePage"));
const ListPropertyPage = lazy(() => import("./components/ListPropertyPage"));
const MyListings = lazy(() => import("./components/MyListings"));
const PropertyDetail = lazy(() => import("./components/PropertyDetail.jsx"));
const SchedulePage = lazy(() => import("./components/SchedulePage.jsx"));
const ScheduleAppointment = lazy(() => import("./components/ScheduleAppointment.jsx"));
const PropertyViewings = lazy(() => import("./components/PropertyViewings.jsx"));

// Loading component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
  </div>
);

function App() {
  return (
    <Router>
      <AuthProvider>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            <Route path="/" element={<Home />} />

            <Route
              path="/login"
              element={
                <GuestRoute>
                  <LoginPage />
                </GuestRoute>
              }
            />
            <Route
              path="/createAccount"
              element={
                <GuestRoute>
                  <SignIn />
                </GuestRoute>
              }
            />
            <Route path="/verify" element={<VerifyEmail />} />
            <Route path="/search" element={<ListingsPage />} />

            {/* Protected Routes */}
            <Route
              path="/profile"
              element={
                <PrivateRoute>
                  <ProfilePage />
                </PrivateRoute>
              }
            />
            <Route
              path="/listproperties"
              element={
                <AgentRoute>
                  <ListPropertyPage />
                </AgentRoute>
              }
            />
            <Route
              path="/myListings"
              element={
                <AgentRoute>
                  <MyListings />
                </AgentRoute>
              }
            />
            <Route
              path="/schedule/:id"
              element={
                <AgentRoute>
                  <SchedulePage />
                </AgentRoute>
              }
            />
            <Route
              path="/mylistings/viewings/:propertyId"
              element={
                <AgentRoute>
                  <PropertyViewings />
                </AgentRoute>
              }
            />

            <Route
              path="/property/:id"
              element={
                <PrivateRoute>
                  <PropertyDetail />
                </PrivateRoute>
              }
            />
            <Route
              path="/scheduleAppointMent/:propertyId"
              element={
                <PrivateRoute>
                  <ScheduleAppointment />
                </PrivateRoute>
              }
            />
          </Routes>
        </Suspense>
      </AuthProvider>
    </Router>
  );
}

export default App;
