import { initializeApp } from "firebase/app";
import {
  getAuth,
  GoogleAuthProvider,
  FacebookAuthProvider,
  OAuthProvider
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
  apiKey: "AIzaSyDimws1TevrfG4Lns55pkjPxUnGuWZE1MQ",
  authDomain: "findmygaff-d17df.firebaseapp.com",
  projectId: "findmygaff-d17df",
  storageBucket: "findmygaff-d17df.firebasestorage.app",
  messagingSenderId: "872246557687",
  appId: "1:872246557687:web:49f79e58095e5e9891ccf2",
  measurementId: "G-T4FN5MPK50"
};


const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const provider = new GoogleAuthProvider();
const facebookProvider = new FacebookAuthProvider();
facebookProvider.addScope("email");
const appleProvider = new OAuthProvider("apple.com");
appleProvider.addScope("email");
appleProvider.addScope("name");
export { auth, db, storage, provider, facebookProvider, appleProvider };
