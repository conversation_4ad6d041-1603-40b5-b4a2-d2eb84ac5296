import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthProvider";
import { useEffect, useState } from "react";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "../firebase";

const AgentRoute = ({ children }) => {
  const { user } = useAuth();
  const [isAgent, setIsAgent] = useState(null); // null = loading

  useEffect(() => {
    const checkIsAgent = async () => {
      if (user?.email) {
        const q = query(collection(db, "profiles"), where("email", "==", user.email));
        const snapshot = await getDocs(q);

        if (!snapshot.empty) {
          const docData = snapshot.docs[0].data();
          setIsAgent(docData.isAgent === true);
        } else {
          setIsAgent(false);
        }
      } else {
        setIsAgent(false);
      }
    };

    checkIsAgent();
  }, [user]);

  if (!user) return <Navigate to="/login" replace />;
  if (isAgent === null) return <div>Loading...</div>;
  if (!isAgent) return <Navigate to="/" replace />;

  return children;
};

export default AgentRoute;
