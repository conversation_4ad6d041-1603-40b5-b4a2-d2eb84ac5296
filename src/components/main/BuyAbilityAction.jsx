import React, { Component } from 'react'

export default class BuyAbilityAction extends Component {
  render() {
    return (
      <section
          className="py-20 px-4 bg-gray-100"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-offset="200"
        >
          <div className="max-w-6xl mx-auto space-y-8">
            <h2 className="text-3xl font-bold text-center mb-8">
              Find homes you can afford with BuyAbility℠
            </h2>
            <div className="grid gap-6 grid-cols-1 md:grid-cols-4 items-start">
              {/* Mock Mortgage widget */}
              <div className="bg-white p-6 rounded-lg shadow-lg text-center space-y-4">
                <div className="text-xl font-semibold">Find My GAFF Home Loans</div>
                <div className="space-y-1 text-gray-600">
                  <div>Suggested target price: —</div>
                  <div>Monthly payment: —</div>
                  <div>Today's rate: —%</div>
                  <div>APR: —%</div>
                </div>
                <button className="mt-4 w-full py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                  Let's get started
                </button>
              </div>
              {/* Placeholder cards */}
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white p-4 rounded-lg shadow-lg">
                  <div className="bg-gray-200 h-40 rounded mb-4 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse w-5/6"></div>
                </div>
              ))}
            </div>
          </div>
        </section>
    )
  }
}
