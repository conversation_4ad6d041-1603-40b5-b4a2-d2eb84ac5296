import React from "react";
import { useNavigate } from "react-router-dom";

export default function Promos() {
  const navigate = useNavigate();

  return (
    <section className="py-20 px-4 bg-gray-50" data-aos="fade-up" data-aos-duration="1000" data-aos-offset="200">
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
        {[
          {
            title: "Buy a GAFF",
            desc: "Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.",
            cta: "Browse homes",
            link: "/search?type=buy",
            image: "https://www.zillowstatic.com/bedrock/app/uploads/sites/5/2024/04/homepage-spot-agent-lg-1.webp"
          },
          {
            title: "Sell a GAFF",
            desc: "No matter what path you take to sell your home, we can help you navigate a successful sale.",
            cta: "See your options",
            link: "/search",
            image: "https://www.zillowstatic.com/bedrock/app/uploads/sites/5/2024/04/homepage-spot-sell-lg-1.webp"
          },
          {
            title: "Rent a GAFF",
            desc: "We're creating a seamless online experience – from shopping on the largest rental network, to applying, to paying rent.",
            cta: "Find rentals",
            link: "/search?type=rent",
            image: "https://www.zillowstatic.com/bedrock/app/uploads/sites/5/2024/04/homepage-spot-rent-lg-1.webp"
          }
        ].map(({ title, desc, cta, link, image }) => (
          <div key={title} className="bg-white rounded-2xl shadow-md overflow-hidden hover:shadow-xl transition duration-300">
            <img src={image} alt={title} className="w-48 h-48 object-cover rounded-lg mx-auto" />
            <div className="p-6 space-y-4 text-center">
              <h3 className="text-2xl font-bold">{title}</h3>
              <p className="text-gray-600">{desc}</p>
              <button onClick={() => navigate(link)} className="mt-4 px-5 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition">
                {cta}
              </button>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
