import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { db, auth } from "../firebase";
import { getDoc, doc, addDoc, collection, query, where, getDocs } from "firebase/firestore";
import Header from "./Header";
import { Calendar, Clock, MapPin, Star, Phone, MessageCircle, CheckCircle, AlertCircle } from "lucide-react";

export default function ScheduleAppointment() {
  const { propertyId } = useParams();
  const [property, setProperty] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [notification, setNotification] = useState(null);

  useEffect(() => {
    const fetchProperty = async () => {
      const docRef = doc(db, "properties", propertyId);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        setProperty({ id: docSnap.id, ...docSnap.data() });
      }
      setLoading(false);
    };
    fetchProperty();
  }, [propertyId]);

  const showNotification = (message, type = 'info') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleSchedule = async () => {
    const user = auth.currentUser;
    if (!user || !selectedDate) return;

    setSubmitting(true);

    try {
      // Check for duplicate viewing
      const viewingsRef = collection(db, "viewings");
      const q = query(viewingsRef, where("userEmail", "==", user.email), where("propertyId", "==", propertyId), where("scheduledDate", "==", selectedDate));

      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
        showNotification("You've already scheduled a viewing for this time.", 'error');
        setSubmitting(false);
        return;
      }

      // Fetch phoneFull and whatsappFull from profile if present
      let phoneFull = "";
      let whatsappFull = "";
      const profileRef = query(collection(db, "profiles"), where("email", "==", user.email));
      const profileSnap = await getDocs(profileRef);
      if (!profileSnap.empty) {
        const profileData = profileSnap.docs[0].data();
        if (profileData.phoneFull) phoneFull = profileData.phoneFull;
        if (profileData.whatsappFull) whatsappFull = profileData.whatsappFull;
      }

      // Add new viewing
      await addDoc(viewingsRef, {
        userEmail: user.email,
        agentEmail: property.agentEmail,
        propertyId,
        scheduledDate: selectedDate,
        createdAt: new Date().toISOString(),
        ...(phoneFull && { phoneFull }),
        ...(whatsappFull && { whatsappFull }),
      });

      showNotification("Viewing scheduled successfully!", 'success');
      setSelectedDate(null);
    } catch (error) {
      console.error("Error scheduling viewing:", error);
      showNotification("Failed to schedule viewing. Please try again.", 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  // Sort schedules in ascending order by date
  const getSortedSchedules = () => {
    if (!property || !property.schedules) return [];

    return [...property.schedules].sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });
  };

  if (loading) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20">
          <div className="max-w-4xl mx-auto px-4 py-8">
            <div className="animate-pulse">
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div className="h-80 bg-gray-300"></div>
                <div className="p-8">
                  <div className="h-8 bg-gray-300 rounded mb-4"></div>
                  <div className="h-4 bg-gray-300 rounded mb-6"></div>
                  <div className="space-y-3">
                    <div className="h-12 bg-gray-300 rounded"></div>
                    <div className="h-12 bg-gray-300 rounded"></div>
                    <div className="h-12 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (!property) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20">
          <div className="max-w-4xl mx-auto px-4 py-8">
            <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
              <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Property Not Found</h2>
              <p className="text-gray-600">The property you're looking for doesn't exist or has been removed.</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  const sortedSchedules = getSortedSchedules();

  return (
    <>
      <Header />

      {/* Notification */}
      {notification && (
        <div className={`fixed top-24 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center gap-3 transition-all duration-300 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          {notification.type === 'success' && <CheckCircle className="h-5 w-5" />}
          {notification.type === 'error' && <AlertCircle className="h-5 w-5" />}
          <span className="font-medium">{notification.message}</span>
        </div>
      )}

      <div className="min-h-screen bg-gradient-to-br from-white-50 to-white-100 pt-20">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            {/* Property Image */}
            <div className="relative h-80 sm:h-80 overflow-hidden">
              <img
                src={property.imageUrls?.[0]}
                alt="Property"
                className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
            </div>

            {/* Property Details */}
            <div className="p-6 sm:p-8">
              <div className="flex items-start gap-3 mb-6">
                <MapPin className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                    {property.address}
                  </h1>
                  <p className="text-gray-600 text-lg leading-relaxed">
                    {property.usp}
                  </p>
                </div>
              </div>

              {/* Schedule Selection */}
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-6">
                  <Calendar className="h-6 w-6 text-blue-600" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    Available Viewing Times
                  </h2>
                </div>

                {sortedSchedules.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-xl">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg font-medium">No schedules available</p>
                    <p className="text-gray-400 mt-2">Please contact the agent for more information</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {sortedSchedules.map((s, index) => {
                      const { date, time } = formatDate(s.date);
                      const isSelected = selectedDate === s.date;

                      return (
                        <button
                          key={index}
                          onClick={() => setSelectedDate(s.date)}
                          className={`group relative p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                            isSelected
                              ? 'border-blue-600 bg-blue-600 text-white shadow-lg transform scale-105'
                              : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-md hover:bg-blue-50'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <Calendar className={`h-5 w-5 ${isSelected ? 'text-white' : 'text-blue-600'}`} />
                            <div>
                              <div className={`font-semibold ${isSelected ? 'text-white' : 'text-gray-900'}`}>
                                {date}
                              </div>
                              <div className={`text-sm ${isSelected ? 'text-blue-100' : 'text-gray-500'}`}>
                                {time}
                              </div>
                            </div>
                          </div>
                          {isSelected && (
                            <CheckCircle className="absolute top-3 right-3 h-5 w-5 text-white" />
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleSchedule}
                  disabled={!selectedDate || submitting}
                  className="flex-1 relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100"
                >
                  <div className="flex items-center justify-center gap-3">
                    {submitting ? (
                      <>
                        <svg className="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24" fill="none">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                        </svg>
                        <span>Scheduling...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-5 w-5" />
                        <span>Confirm Appointment</span>
                      </>
                    )}
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
