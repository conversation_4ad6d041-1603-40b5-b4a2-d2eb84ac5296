import React, { useState, useEffect, useRef } from "react";
import { FiSearch, FiMenu, FiX, FiCalendar, FiMail } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import { auth } from "../firebase";
import { onAuthStateChanged, signOut } from "firebase/auth";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "../firebase";

const Header = () => {
  const navigate = useNavigate();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isAgent, setIsAgent] = useState(false);
  const dropdownTimeout = useRef(null); // Ref for delayed close

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 450);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        try {
          const profilesRef = collection(db, "profiles");
          const q = query(profilesRef, where("email", "==", currentUser.email));
          const querySnapshot = await getDocs(q);

          if (!querySnapshot.empty) {
            const profileData = querySnapshot.docs[0].data();
            setIsAgent(profileData?.isAgent === true);
          } else {
            console.warn("No profile found for:", currentUser.email);
            setIsAgent(false);
          }
        } catch (error) {
          console.error("Error fetching profile:", error);
          setIsAgent(false);
        }
      } else {
        setIsAgent(false);
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    return () => {
      if (dropdownTimeout.current) clearTimeout(dropdownTimeout.current);
    };
  }, []);

  const handleMouseEnter = () => {
    if (dropdownTimeout.current) clearTimeout(dropdownTimeout.current);
    setDropdownOpen(true);
  };

  const handleMouseLeave = () => {
    dropdownTimeout.current = setTimeout(() => {
      setDropdownOpen(false);
    }, 200); // Delay prevents flicker
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogout = async () => {
    await signOut(auth);
    setDropdownOpen(false);
    navigate("/");
  };

  const handleCalendarClick = () => {
    // Navigate to calendar page or open calendar modal
    navigate("/calendar");
  };

  const handleInboxClick = () => {
    // Navigate to inbox page or open inbox modal
    navigate("/inbox");
  };

  return (
    <header
      className={`w-full fixed top-0 z-50 bg-white shadow-md transition-all duration-300 ${
        scrolled ? "py-3" : "py-5"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 flex items-center justify-between relative">
        {!scrolled && !mobileMenuOpen && (
          <div className="hidden md:flex space-x-6 text-sm text-gray-700 font-medium">
            <button
              className="cursor-pointer"
              onClick={() => navigate("/search?type=buy")}
            >
              Buy
            </button>
            <button
              className="cursor-pointer"
              onClick={() => navigate("/search?type=rent")}
            >
              Rent
            </button>

            {/* <button className="cursor-pointer">Sell</button> */}

          </div>
        )}

        {!scrolled ? (
          <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center space-x-2">
            <img
              src="/images/logoCrop.jpg"
              alt="Logo"
              className="h-14 w-auto object-contain cursor-pointer"
              onClick={() => navigate("/")}
            />
            <span
              className="text-xl font-bold text-gray-800 cursor-pointer"
              onClick={() => navigate("/")}
            >
            </span>
          </div>
        ) : (
          <>
            <div className="flex items-center space-x-2">
              <img
                src="/images/logoCrop.jpg"
                alt="Logo"
                className="h-10 w-auto object-contain cursor-pointer"
                onClick={() => navigate("/")}
              />
              <span
                onClick={() => navigate("/")}
                className="text-xl font-bold text-gray-800 cursor-pointer"
              >
                Find My Gaff
              </span>
            </div>
            <div className="absolute left-1/2 transform -translate-x-1/2 w-full max-w-2xl hidden md:block">
              <div className="relative w-full">
                <input
                  type="text"
                  placeholder="Enter an address, neighborhood, city, or ZIP code"
                  onClick={() => navigate("/search")}
                  className="w-full rounded-full border border-gray-300 px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <FiSearch
                  onClick={() => navigate("/search")}
                  className="absolute top-2.5 left-3 text-gray-500"
                />
              </div>
            </div>
          </>
        )}

        {!scrolled && !mobileMenuOpen && (
          <div className="hidden md:flex space-x-4 text-sm text-gray-700 font-medium items-center relative">
            <button className="cursor-pointer">Share</button>
            <button className="cursor-pointer">Brand Call</button>

            {/* Calendar Button */}
            <button
              onClick={handleCalendarClick}
              className="cursor-pointer p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              title="Calendar"
            >
              <FiCalendar className="w-5 h-5 text-gray-600" />
            </button>

            {/* Inbox Button */}
            <button
              onClick={handleInboxClick}
              className="cursor-pointer p-2 rounded-full hover:bg-gray-100 transition-colors duration-200 relative"
              title="Inbox"
            >
              <FiMail className="w-5 h-5 text-gray-600" />
              {/* Optional: Add notification badge */}
              {/* <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span> */}
            </button>

            {user ? (
              <div
                className="relative"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <button className="bg-blue-600 text-white rounded-full h-8 w-8 flex items-center justify-center uppercase">
                  {user.email[0]}
                </button>
                {dropdownOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white border rounded shadow-md text-sm z-50">
                    <button
                      onClick={() => {
                        navigate("/profile");
                        setDropdownOpen(false);
                      }}
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                    >
                      Profile
                    </button>
                    {isAgent && (
                      <button
                        onClick={() => {
                          // navigate("/list-property");
                          navigate("/listproperties");
                          setDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                      >
                        List a New Property
                      </button>
                    )}
                    {isAgent && (
                      <button
                        onClick={() => {
                          // navigate("/list-property");
                          navigate("/myListings");
                          setDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                      >
                        My Listings
                      </button>
                    )}
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                    >
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={() => navigate("/login")}
                className="cursor-pointer"
              >
                Sign In
              </button>
            )}
          </div>
        )}

        <div className="md:hidden">
          <button onClick={toggleMobileMenu} className="text-gray-700 text-xl">
            {mobileMenuOpen ? <FiX /> : <FiMenu />}
          </button>
        </div>
      </div>

      {scrolled && (
        <div className="px-4 mt-2 md:hidden">
          <div className="relative w-full">
            <input
              type="text"
              placeholder="Search for address, ZIP code, etc."
              className="w-full rounded-full border border-gray-300 px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <FiSearch className="absolute top-2.5 left-3 text-gray-500" />
          </div>
        </div>
      )}

      {mobileMenuOpen && (
        <div className="md:hidden bg-white shadow-inner px-4 pb-4 pt-2 space-y-2 text-gray-700 text-sm font-medium">
          <button
            onClick={() => {
              setMobileMenuOpen(false);
              navigate("/search?type=buy");
            }}
            className="block w-full text-left"
          >
            Buy
          </button>
          <button
            onClick={() => {
              setMobileMenuOpen(false);
              navigate("/search?type=rent");
            }}
            className="block w-full text-left"
          >
            Rent
          </button>

          {/* Mobile Calendar and Inbox buttons */}
          <button
            onClick={() => {
              setMobileMenuOpen(false);
              handleCalendarClick();
            }}
            className="block w-full text-left"
          >
            Calendar
          </button>
          <button
            onClick={() => {
              setMobileMenuOpen(false);
              handleInboxClick();
            }}
            className="block w-full text-left"
          >
            Inbox
          </button>

          {isAgent && (
            <a href="#" className="block">
              List a New Property
            </a>
          )}
          {isAgent && (
            <a href="#" className="block">
              My Listings
            </a>
          )}
          {user ? (
            <button
              onClick={handleLogout}
              className="block w-full text-left px-4 py-2 hover:bg-gray-100"
            >
              Logout
            </button>
          ) : (
            <a onClick={() => navigate("/login")} className="block">
              Sign In
            </a>
          )}
        </div>
      )}
    </header>
  );
};

export default Header;
