import React, { useState, useRef, useEffect } from "react";
import { db, storage } from "../firebase";
import { collection, doc, setDoc, serverTimestamp } from "firebase/firestore";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { useAuth } from "../context/AuthProvider";
import Header from "./Header";

export default function ListPropertyPage() {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    salePrice: "",
    areaSize: "",
    usp: "",
    address: "",
    mgmt: "",
    propertyType: "",
    eirCode: "",
    bedrooms: 0,
    bathrooms: 0,
    parkingSpaces: 0,
    builtYear: "",
    berRating: "",
    description: "",
    isFeatured: false,
    saleOrRent: true,
    photos: [],
    layoutImage: null,
    videoFile: null,
  });

  const [loading, setLoading] = useState(false);
  const [isBerDropdownOpen, setIsBerDropdownOpen] = useState(false);
  const berDropdownRef = useRef(null);

  const berRatingOptions = ["A1", "A2", "A3", "B1", "B2", "B3", "C1", "C2", "C3", "D1", "D2", "E1", "E2", "F", "G"];

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (berDropdownRef.current && !berDropdownRef.current.contains(event.target)) {
        setIsBerDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    if (type === "file") {
      if (name === "photos") {
        const selected = Array.from(files).slice(0, 30);
        setFormData((prev) => ({ ...prev, photos: selected }));
      } else if (name === "layoutImage" && files[0]) {
        setFormData((prev) => ({ ...prev, layoutImage: files[0] }));
      } else if (name === "videoFile" && files[0]) {
        setFormData((prev) => ({ ...prev, videoFile: files[0] }));
      }
    } else if (type === "checkbox") {
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else if (name === "bedrooms" || name === "bathrooms" || name === "parkingSpaces") {
      // Ensure these fields cannot be negative
      const numValue = Math.max(0, parseInt(value) || 0);
      setFormData((prev) => ({ ...prev, [name]: numValue }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleBerRatingSelect = (rating) => {
    setFormData((prev) => ({ ...prev, berRating: rating }));
    setIsBerDropdownOpen(false);
  };

  const fetchSaleData = async (address, eirCode) => {
    try {
      const query = `${address} ${eirCode}`.trim();
      const res = await fetch(`https://priceregister.civictech.ie/api/v1/residential/sales?search=${encodeURIComponent(query)}&limit=1`);
      const data = await res.json();
      if (data?.data?.[0]) {
        return {
          date_of_sale: data.data[0].date_of_sale,
          price_in_euro: data.data[0].price_in_euros,
        };
      }
    } catch (error) {
      console.error("Failed to fetch sale data", error);
    }
    return { date_of_sale: null, price_in_euro: null };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!user) return;

    // Validate required fields
    const requiredFields = [
      "salePrice", "areaSize", "usp", "address", "mgmt", "propertyType",
      "eirCode", "bedrooms", "bathrooms", "parkingSpaces", "builtYear",
      "berRating", "description",
    ];
    for (let field of requiredFields) {
      if (!formData[field] && formData[field] !== 0) {
        alert(`Please fill in the ${field}`);
        return;
      }
    }
    if (formData.photos.length === 0) {
      alert("Please upload at least one photo");
      return;
    }
    if (!formData.layoutImage) {
      alert("Please upload a layout image");
      return;
    }
    if (!formData.videoFile) {
      alert("Please upload a property video");
      return;
    }

    setLoading(true);
    const propertyRef = doc(collection(db, "properties"));
    const propertyId = propertyRef.id;

    const imageUrls = await Promise.all(
      formData.photos.map(async (file, index) => {
        const imageRef = ref(storage, `properties/${propertyId}/${index}.jpg`);
        await uploadBytes(imageRef, file);
        return await getDownloadURL(imageRef);
      })
    );

    const layoutImageUrl = await (async () => {
      const layoutRef = ref(storage, `properties/${propertyId}/layout.jpg`);
      await uploadBytes(layoutRef, formData.layoutImage);
      return await getDownloadURL(layoutRef);
    })();

    const videoUrl = await (async () => {
      const videoRef = ref(storage, `properties/${propertyId}/video.mp4`);
      await uploadBytes(videoRef, formData.videoFile);
      return await getDownloadURL(videoRef);
    })();

    const saleData = await fetchSaleData(formData.address, formData.eirCode);

    const finalData = {
      ...formData,
      imageUrls,
      layoutImageUrl,
      videoUrl,
      createdAt: serverTimestamp(),
      agentEmail: user.email,
      ...saleData,
    };

    delete finalData.photos;
    delete finalData.layoutImage;
    delete finalData.videoFile;

    await setDoc(propertyRef, finalData);
    alert("Property listed successfully!");
    setLoading(false);
    window.location.reload();
  };

  return (
    <div
      className="min-h-screen"
      style={{
        backgroundImage: `url('https://maps.googleapis.com/maps/api/staticmap?center=Ireland&zoom=6&size=1200x800&maptype=roadmap&key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}')`,
      }}
    >
      <Header />
      <div className="max-w-5xl mx-auto pt-25 bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">List A New Property</h2>
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* LEFT COLUMN */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sale or Rent Price</label>
            <input name="salePrice" value={formData.salePrice} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Area Size in Sq Meters</label>
            <input name="areaSize" value={formData.areaSize} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">USP (Highlight Feature)</label>
            <input name="usp" value={formData.usp} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Full Address</label>
            <input name="address" value={formData.address} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">MGMT Charges</label>
            <input name="mgmt" value={formData.mgmt} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Property Type</label>
            <input name="propertyType" value={formData.propertyType} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">EIR Code</label>
            <input name="eirCode" value={formData.eirCode} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Property Description</label>
            <textarea name="description" value={formData.description} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" rows={4} />
          </div>

          {/* RIGHT COLUMN */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Bedrooms</label>
            <input
              name="bedrooms"
              type="number"
              min="0"
              value={formData.bedrooms}
              onChange={handleChange}
              required
              className="w-full border border-gray-300 rounded-xl px-3 py-2"
            />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Bathrooms</label>
            <input
              name="bathrooms"
              type="number"
              min="0"
              value={formData.bathrooms}
              onChange={handleChange}
              required
              className="w-full border border-gray-300 rounded-xl px-3 py-2"
            />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Garages or Parking Spaces</label>
            <input
              name="parkingSpaces"
              type="number"
              min="0"
              value={formData.parkingSpaces}
              onChange={handleChange}
              required
              className="w-full border border-gray-300 rounded-xl px-3 py-2"
            />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Year Built</label>
            <input name="builtYear" value={formData.builtYear} onChange={handleChange} required className="w-full border border-gray-300 rounded-xl px-3 py-2" />

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">BER Rating</label>
            <div className="relative" ref={berDropdownRef}>
              <button
                type="button"
                onClick={() => setIsBerDropdownOpen(!isBerDropdownOpen)}
                className="w-full border border-gray-300 rounded-xl px-3 py-2 text-left bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {formData.berRating || "Select BER Rating"}
                <span className="float-right">▼</span>
              </button>
              {isBerDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-xl shadow-lg max-h-60 overflow-auto">
                  {berRatingOptions.map((rating) => (
                    <div
                      key={rating}
                      onClick={() => handleBerRatingSelect(rating)}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                    >
                      {rating}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">Mark this property as featured?</label>
            <div className="flex items-center gap-6">
              <label className="flex items-center gap-2">
                <input type="radio" name="isFeatured" checked={formData.isFeatured} onChange={() => setFormData({ ...formData, isFeatured: true })} />
                Yes
              </label>
              <label className="flex items-center gap-2">
                <input type="radio" name="isFeatured" checked={!formData.isFeatured} onChange={() => setFormData({ ...formData, isFeatured: false })} />
                No
              </label>
            </div>

            <label className="block text-sm font-medium text-gray-700 mb-1 mt-4">For Sale Or Rent:</label>
            <div className="flex items-center gap-6">
              <label className="flex items-center gap-2">
                <input type="radio" name="saleOrRent" checked={formData.saleOrRent} onChange={() => setFormData({ ...formData, saleOrRent: true })} />
                For Sale
              </label>
              <label className="flex items-center gap-2">
                <input type="radio" name="saleOrRent" checked={!formData.saleOrRent} onChange={() => setFormData({ ...formData, saleOrRent: false })} />
                For Rent
              </label>
            </div>
          </div>

          {/* File Uploads */}
          <div className="col-span-1 md:col-span-2">
            <label className="block font-medium text-gray-700 mb-2">Upload Property Photos (max 30)</label>
            <input type="file" name="photos" accept="image/*" multiple required onChange={handleChange} className="w-full border border-gray-300 rounded-xl px-4 py-2" />
          </div>
          <div className="col-span-1 md:col-span-2">
            <label className="block font-medium text-gray-700 mb-2">Upload Property Layout (1 image)</label>
            <input type="file" name="layoutImage" accept="image/*" required onChange={handleChange} className="w-full border border-gray-300 rounded-xl px-4 py-2" />
          </div>
          <div className="col-span-1 md:col-span-2">
            <label className="block font-medium text-gray-700 mb-2">Upload Property Video</label>
            <input type="file" name="videoFile" accept="video/*" required onChange={handleChange} className="w-full border border-gray-300 rounded-xl px-4 py-2" />
          </div>

          <div className="col-span-1 md:col-span-2 flex justify-end">
            <button type="submit" disabled={loading} className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700">
              {loading ? "Listing..." : "Submit Property"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
