import { FcGoogle } from "react-icons/fc";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaFacebookF } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { auth, provider, db } from "../firebase";
import {
  signInWithPopup,
  createUserWithEmailAndPassword,
  sendEmailVerification,
  signOut,
  FacebookAuthProvider,
  OAuthProvider,
} from "firebase/auth";
import {
  doc,
  setDoc,
  getDoc,
  query,
  collection,
  where,
  getDocs,
} from "firebase/firestore";
import { useState } from "react";

export default function SignIn() {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleClick = () => {
    navigate("/login");
  };

  const createProfileIfNotExists = async (uid, userEmail) => {
    const profilesRef = collection(db, "profiles");
    const q = query(profilesRef, where("email", "==", userEmail));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      throw new Error("An account with this email already exists.");
    }

    const profileRef = doc(db, "profiles", uid);
    await setDoc(profileRef, {
      email: userEmail,
      address: "",
      area: "",
      favoritePlace: "",
    });
  };

  const handleSocialSignIn = async (provider, providerName) => {
    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      const profilesRef = collection(db, "profiles");
      const q = query(profilesRef, where("email", "==", user.email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        alert(
          `An account with this email already exists. Please sign in instead.`
        );
        await signOut(auth);
        navigate("/login");
        return;
      }

      const profileRef = doc(db, "profiles", user.uid);
      await setDoc(profileRef, {
        email: user.email,
        address: "",
        area: "",
        favoritePlace: "",
      });

      console.log(`Signed in as ${user.email} via ${providerName}`);
      navigate("/");
    } catch (error) {
      console.error(`${providerName} Sign-In Error:`, error.message);
      alert(error.message);
    }
  };

  const handleGoogleSignIn = () => handleSocialSignIn(provider, "Google");
  const handleFacebookSignIn = () =>
    handleSocialSignIn(new FacebookAuthProvider(), "Facebook");
  const handleAppleSignIn = () =>
    handleSocialSignIn(new OAuthProvider("apple.com"), "Apple");

  const handleEmailSignup = async (e) => {
    e.preventDefault();
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      await createProfileIfNotExists(user.uid, user.email);
      await sendEmailVerification(user);

      console.log("Verification email sent to:", user.email);
      navigate("/verify");
      await signOut(auth);
      setEmail("");
      setPassword("");
      alert("Verification email sent. Please check your inbox.");
    } catch (error) {
      if (error.message === "An account with this email already exists.") {
        alert("This email is already in use. Please sign in instead.");
        await signOut(auth);
      } else if (error.code === "auth/email-already-in-use") {
        alert("This email already has an account. Try logging in.");
      } else {
        alert(error.message);
      }
      console.error("Email Signup Error:", error.message);
    }
  };

  return (
    <div className="min-h-screen flex">
      <div className="w-full md:w-1/2 lg:w-1/3 flex items-center justify-center px-6 bg-white">
        <div className="max-w-sm w-full space-y-6">
          <div className="text-left">
            <img
              className="h-6 mb-6 cursor-pointer"
              src="https://www.GAFFstatic.com/static/images/logo-GAFF-full.png"
              alt="Find My Gaff"
              onClick={() => navigate("/")}
            />
            <h2 className="text-2xl font-semibold text-gray-900">
              Create Account
            </h2>
          </div>

          <form onSubmit={handleEmailSignup} className="space-y-4">
            <input
              type="email"
              placeholder="Email Address*"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="block w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 text-sm"
            />
            <input
              type="password"
              placeholder="Password*"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="block w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 text-sm"
            />
            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md text-sm"
            >
              Continue
            </button>
          </form>

          <div className="text-sm text-gray-600 text-center">
            Have a GAFF Account?{" "}
            <button
              onClick={handleClick}
              className="text-blue-600 font-medium hover:underline"
            >
              Sign In
            </button>
          </div>

          <div className="flex items-center gap-2 text-gray-400 text-sm">
            <div className="flex-1 h-px bg-gray-300" />
            OR
            <div className="flex-1 h-px bg-gray-300" />
          </div>

          <div className="space-y-3">
            <button
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FcGoogle className="w-5 h-5" />
              Continue with Google
            </button>
            <button
              onClick={handleAppleSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FaApple className="w-5 h-5" />
              Continue with Apple
            </button>
            <button
              onClick={handleFacebookSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FaFacebookF className="w-5 h-5 text-blue-600" />
              Continue with Facebook
            </button>
          </div>

          <p className="text-xs text-gray-500 text-center">
            By submitting, I accept GAFF's{" "}
            <a href="#" className="text-blue-600 hover:underline">
              terms of use
            </a>
          </p>
        </div>
      </div>

      <div className="hidden md:block md:w-1/2 lg:w-2/3">
        <img
          className="h-full w-full object-cover"
          src="https://images.unsplash.com/photo-1507089947368-19c1da9775ae?auto=format&fit=crop&q=80&w=1600"
          alt="House"
        />
      </div>
    </div>
  );
}
