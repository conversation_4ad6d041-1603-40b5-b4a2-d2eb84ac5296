import React, { useState, useEffect } from "react";
import { db } from "../firebase";
import { useAuth } from "../context/AuthProvider";
import { collection, query, where, getDocs, setDoc, doc, updateDoc } from "firebase/firestore";
import Header from "./Header";
import { User, MapPin, Phone, MessageSquare, Building, Edit3, Save } from "lucide-react";

const countryCodes = [
  { code: "+54", label: "Argentina (+54)" },
  { code: "+61", label: "Australia (+61)" },
  { code: "+880", label: "Bangladesh (+880)" },
  { code: "+32", label: "Belgium (+32)" },
  { code: "+591", label: "Bolivia (+591)" },
  { code: "+55", label: "Brazil (+55)" },
  { code: "+86", label: "China (+86)" },
  { code: "+57", label: "Colombia (+57)" },
  { code: "+385", label: "Croatia (+385)" },
  { code: "+420", label: "Czech Republic (+420)" },
  { code: "+45", label: "Denmark (+45)" },
  { code: "+20", label: "Egypt (+20)" },
  { code: "+593", label: "Ecuador (+593)" },
  { code: "+33", label: "France (+33)" },
  { code: "+49", label: "Germany (+49)" },
  { code: "+30", label: "Greece (+30)" },
  { code: "+36", label: "Hungary (+36)" },
  { code: "+91", label: "India (+91)" },
  { code: "+62", label: "Indonesia (+62)" },
  { code: "+353", label: "Ireland (+353)" },
  { code: "+972", label: "Israel (+972)" },
  { code: "+39", label: "Italy (+39)" },
  { code: "+81", label: "Japan (+81)" },
  { code: "+254", label: "Kenya (+254)" },
  { code: "+60", label: "Malaysia (+60)" },
  { code: "+52", label: "Mexico (+52)" },
  { code: "+505", label: "Nicaragua (+505)" },
  { code: "+31", label: "Netherlands (+31)" },
  { code: "+64", label: "New Zealand (+64)" },
  { code: "+47", label: "Norway (+47)" },
  { code: "+92", label: "Pakistan (+92)" },
  { code: "+507", label: "Panama (+507)" },
  { code: "+595", label: "Paraguay (+595)" },
  { code: "+51", label: "Peru (+51)" },
  { code: "+63", label: "Philippines (+63)" },
  { code: "+48", label: "Poland (+48)" },
  { code: "+351", label: "Portugal (+351)" },
  { code: "+40", label: "Romania (+40)" },
  { code: "+7", label: "Russia (+7)" },
  { code: "+65", label: "Singapore (+65)" },
  { code: "+421", label: "Slovakia (+421)" },
  { code: "+386", label: "Slovenia (+386)" },
  { code: "+27", label: "South Africa (+27)" },
  { code: "+82", label: "South Korea (+82)" },
  { code: "+34", label: "Spain (+34)" },
  { code: "+94", label: "Sri Lanka (+94)" },
  { code: "+46", label: "Sweden (+46)" },
  { code: "+41", label: "Switzerland (+41)" },
  { code: "+255", label: "Tanzania (+255)" },
  { code: "+66", label: "Thailand (+66)" },
  { code: "+90", label: "Turkey (+90)" },
  { code: "+256", label: "Uganda (+256)" },
  { code: "+971", label: "UAE (+971)" },
  { code: "+44", label: "United Kingdom (+44)" },
  { code: "+1", label: "USA/Canada (+1)" },
  { code: "+598", label: "Uruguay (+598)" },
  { code: "+58", label: "Venezuela (+58)" },
  { code: "+84", label: "Vietnam (+84)" },
];

export default function ProfilePage() {
  const { user } = useAuth();

  const defaultProfileData = {
    homePlace: "",
    workPlace: "",
    otherPlace: "",
    pincode: "",
    isAgent: false,
    psrLicenseNumber: "",
    agentAddress: "",
    companyName: "",
    companyWebsite: "",
    phoneCountryCode: "+353",
    phoneNumber: "",
    whatsappCountryCode: "+353",
    whatsappNumber: "",
  };

  const [profileData, setProfileData] = useState(defaultProfileData);
  const [docId, setDocId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);

  useEffect(() => {
    const fetchProfileByEmail = async () => {
      if (!user?.email) {
        setLoading(false);
        return;
      }

      const q = query(collection(db, "profiles"), where("email", "==", user.email));

      try {
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const docSnap = querySnapshot.docs[0];
          setDocId(docSnap.id);
          const data = docSnap.data();
          setProfileData({ ...defaultProfileData, ...data });
        } else {
          const newDocRef = doc(collection(db, "profiles"));
          await setDoc(newDocRef, {
            email: user.email,
            ...defaultProfileData,
          });
          setDocId(newDocRef.id);
          setEditMode(true);
        }
      } catch (error) {
        console.error("Error loading profile:", error);
        alert("Something went wrong.");
      }

      setLoading(false);
    };

    fetchProfileByEmail();
  }, [user]);

  const handleChange = (e) => {
    setProfileData({
      ...profileData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSave = async () => {
    if (!docId) return;

    // Agent validation
    if (profileData.isAgent) {
      const requiredFields = ["psrLicenseNumber", "agentAddress", "companyName"];
      for (let field of requiredFields) {
        if (!profileData[field]) {
          alert(`${field.replace(/([A-Z])/g, " $1")} is required for agents.`);
          return;
        }
      }
    }

    const updatedData = {
      ...profileData,
      phoneFull: `${profileData.phoneCountryCode}${profileData.phoneNumber}`,
      whatsappFull: `${profileData.whatsappCountryCode}${profileData.whatsappNumber}`,
      ...(profileData.isAgent
        ? {}
        : {
            psrLicenseNumber: null,
            agentAddress: null,
            companyName: null,
            companyWebsite: null,
          }),
    };

    try {
      const docRef = doc(db, "profiles", docId);
      await updateDoc(docRef, updatedData);
      setEditMode(false);
      alert("Profile updated!");
      window.location.reload();
    } catch (error) {
      console.error("Error saving profile:", error);
      alert("Failed to save profile.");
    }
  };

  if (loading) return <p className="text-center mt-10">Loading profile...</p>;

  return (
    <>
      <Header />
      <div
        className="min-h-screen bg-cover bg-center flex items-center justify-center px-4 py-8"
        style={{
          backgroundImage: `url('https://maps.googleapis.com/maps/api/staticmap?center=Ireland&zoom=6&size=1200x800&maptype=roadmap&key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}')`,
        }}
      >
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8 w-full max-w-4xl mt-20">
          {/* Header Section */}
          <div className="flex flex-col sm:flex-row items-center justify-between mb-8 pb-6 border-b border-gray-200">
            <div className="flex items-center gap-4 mb-4 sm:mb-0">
              <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">Your Profile</h1>
                <p className="text-gray-600 text-sm">Manage your account information</p>
              </div>
            </div>
            <button
              onClick={editMode ? handleSave : () => setEditMode(true)}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${
                editMode
                  ? "bg-emerald-600 text-white hover:bg-emerald-700 shadow-lg"
                  : "bg-gray-700 text-white hover:bg-gray-800 shadow-md"
              }`}
            >
              {editMode ? <Save className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
              {editMode ? "Save Changes" : "Edit Profile"}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Personal Information Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <User className="w-5 h-5 text-emerald-600" />
                <h2 className="text-lg font-semibold text-gray-800">Personal Information</h2>
              </div>

              {/* Email */}
              <div>
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                  <User className="w-4 h-4 text-gray-500" />
                  Email
                </label>
                <input
                  type="email"
                  value={user?.email || ""}
                  readOnly
                  className="w-full px-4 py-3 border-2 border-transparent bg-gray-50 rounded-xl text-gray-600 focus:outline-none"
                />
              </div>

              {/* Location Fields */}
              {[
                { key: "homePlace", label: "Home Or Favorite Place 1", icon: MapPin },
                { key: "workPlace", label: "Work Or Favorite Place 2", icon: Building },
                { key: "otherPlace", label: "Favorite Place 3", icon: MapPin },
              ].map(({ key, label, icon: Icon }) => (
                <div key={key}>
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                    <Icon className="w-4 h-4 text-gray-500" />
                    {label}
                  </label>
                  <input
                    type="text"
                    name={key}
                    value={profileData[key]}
                    onChange={handleChange}
                    readOnly={!editMode}
                    className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                      editMode
                        ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                        : "border-transparent bg-gray-50 text-gray-600"
                    } focus:outline-none placeholder-gray-400`}
                    placeholder={`Enter ${label.toLowerCase()}`}
                  />
                </div>
              ))}

              {/* Pincode */}
              <div>
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  Eircode
                </label>
                <input
                  type="text"
                  name="pincode"
                  value={profileData.pincode}
                  onChange={handleChange}
                  readOnly={!editMode}
                  className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                    editMode
                      ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                      : "border-transparent bg-gray-50 text-gray-600"
                  } focus:outline-none placeholder-gray-400`}
                  placeholder="Enter Eircode"
                />
              </div>
              {/* Contact Information */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Phone className="w-5 h-5 text-emerald-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Contact Information</h2>
                </div>

                {/* Phone Number */}
                <div className="mb-4">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    Phone Number
                  </label>
                  <div className="flex gap-2">
                    <select
                      name="phoneCountryCode"
                      value={profileData.phoneCountryCode}
                      onChange={handleChange}
                      disabled={!editMode}
                      className={`w-1/3 px-3 py-3 border-2 rounded-xl transition-all duration-200 ${
                        editMode
                          ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                          : "border-transparent bg-gray-50 text-gray-600"
                      } focus:outline-none text-sm`}
                    >
                      {countryCodes.map(({ code, label }) => (
                        <option key={code} value={code}>
                          {label}
                        </option>
                      ))}
                    </select>
                    <input
                      type="tel"
                      name="phoneNumber"
                      value={profileData.phoneNumber}
                      onChange={handleChange}
                      readOnly={!editMode}
                      className={`w-2/3 px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                        editMode
                          ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                          : "border-transparent bg-gray-50 text-gray-600"
                      } focus:outline-none placeholder-gray-400`}
                      placeholder="Enter number"
                    />
                  </div>
                </div>

                {/* WhatsApp Number */}
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                    <MessageSquare className="w-4 h-4 text-gray-500" />
                    WhatsApp Number
                  </label>
                  <div className="flex gap-2">
                    <select
                      name="whatsappCountryCode"
                      value={profileData.whatsappCountryCode}
                      onChange={handleChange}
                      disabled={!editMode}
                      className={`w-1/3 px-3 py-3 border-2 rounded-xl transition-all duration-200 ${
                        editMode
                          ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                          : "border-transparent bg-gray-50 text-gray-600"
                      } focus:outline-none text-sm`}
                    >
                      {countryCodes.map(({ code, label }) => (
                        <option key={code} value={code}>
                          {label}
                        </option>
                      ))}
                    </select>
                    <input
                      type="tel"
                      name="whatsappNumber"
                      value={profileData.whatsappNumber}
                      onChange={handleChange}
                      readOnly={!editMode}
                      className={`w-2/3 px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                        editMode
                          ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                          : "border-transparent bg-gray-50 text-gray-600"
                      } focus:outline-none placeholder-gray-400`}
                      placeholder="Enter number"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              {/* Professional Information */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Building className="w-5 h-5 text-emerald-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Professional Information</h2>
                </div>

                {/* Agent Checkbox */}
                <div className="p-4 bg-gray-50 rounded-xl mb-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isAgent"
                      name="isAgent"
                      checked={profileData.isAgent}
                      onChange={(e) =>
                        setProfileData({
                          ...profileData,
                          isAgent: e.target.checked,
                        })
                      }
                      disabled={!editMode}
                      className="h-5 w-5 text-emerald-600 border-2 border-gray-300 rounded focus:ring-emerald-500 focus:ring-2"
                    />
                    <label htmlFor="isAgent" className="ml-3 text-sm font-medium text-gray-700">
                      Are you an agent?
                    </label>
                  </div>
                </div>

                {/* Agent-only Fields */}
                {profileData.isAgent && (
                  <div className="space-y-4">
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Building className="w-4 h-4 text-gray-500" />
                        PSR License Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="psrLicenseNumber"
                        value={profileData.psrLicenseNumber}
                        onChange={handleChange}
                        readOnly={!editMode}
                        className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                          editMode
                            ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                            : "border-transparent bg-gray-50 text-gray-600"
                        } focus:outline-none placeholder-gray-400`}
                        placeholder="Enter license number"
                      />
                    </div>

                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        Agent Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="agentAddress"
                        value={profileData.agentAddress}
                        onChange={handleChange}
                        readOnly={!editMode}
                        className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                          editMode
                            ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                            : "border-transparent bg-gray-50 text-gray-600"
                        } focus:outline-none placeholder-gray-400`}
                        placeholder="Enter address"
                      />
                    </div>

                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Building className="w-4 h-4 text-gray-500" />
                        Company Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={profileData.companyName}
                        onChange={handleChange}
                        readOnly={!editMode}
                        className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                          editMode
                            ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                            : "border-transparent bg-gray-50 text-gray-600"
                        } focus:outline-none placeholder-gray-400`}
                        placeholder="Enter company name"
                      />
                    </div>

                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Building className="w-4 h-4 text-gray-500" />
                        Company Website
                      </label>
                      <input
                        type="text"
                        name="companyWebsite"
                        value={profileData.companyWebsite}
                        onChange={handleChange}
                        readOnly={!editMode}
                        className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 ${
                          editMode
                            ? "border-gray-200 focus:border-emerald-500 focus:ring-4 focus:ring-emerald-50 bg-white shadow-sm"
                            : "border-transparent bg-gray-50 text-gray-600"
                        } focus:outline-none placeholder-gray-400`}
                        placeholder="Enter website (optional)"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Mobile Save Button */}
          <div className="mt-8 lg:hidden">
            <button
              onClick={editMode ? handleSave : () => setEditMode(true)}
              className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-200 flex items-center justify-center gap-2 ${
                editMode
                  ? "bg-emerald-600 text-white hover:bg-emerald-700 shadow-lg"
                  : "bg-gray-700 text-white hover:bg-gray-800 shadow-md"
              }`}
            >
              {editMode ? <Save className="w-5 h-5" /> : <Edit3 className="w-5 h-5" />}
              {editMode ? "Save Changes" : "Edit Profile"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
