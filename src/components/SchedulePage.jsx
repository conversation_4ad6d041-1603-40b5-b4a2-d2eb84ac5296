import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { db } from "../firebase";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { useAuthState } from "react-firebase-hooks/auth"; // Add this import
import { auth } from "../firebase"; // Make sure auth is imported
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Header from "./Header";

export default function SchedulePage() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Authentication state
  const [user, authLoading, authError] = useAuthState(auth);

  const [existingSchedules, setExistingSchedules] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedHour, setSelectedHour] = useState("");
  const [selectedMinute, setSelectedMinute] = useState("");
  const [selectedAmPm, setSelectedAmPm] = useState("");
  const [pendingSchedules, setPendingSchedules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true); // Add page loading state

  // 🔁 Load existing schedules from Firestore and verify agent access
  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        // Wait for auth to load
        if (authLoading) return;

        // Check if user is authenticated
        if (!user) {
          navigate("/");
          return;
        }

        const snapshot = await getDoc(doc(db, "properties", id));
        if (snapshot.exists()) {
          const propertyData = snapshot.data();

          // Check if the current user's email matches the property's agent email
          if (propertyData.agentEmail !== user.email) {
            // Unauthorized access - redirect to home
            navigate("/");
            return;
          }

          // User is authorized, load schedules
          setExistingSchedules(propertyData.schedules || []);
        } else {
          // Property doesn't exist
          navigate("/");
          return;
        }
      } catch (err) {
        console.error("Error fetching schedules:", err);
        navigate("/"); // Redirect on error
      } finally {
        setPageLoading(false);
      }
    };

    fetchSchedules();
  }, [id, user, authLoading, navigate]);

  const handleSaveAll = async () => {
    if (pendingSchedules.length === 0) return alert("No schedules to save.");
    try {
      setLoading(true);
      const docRef = doc(db, "properties", id);

      const newSchedules = pendingSchedules.map((date) => ({
        date: date.toISOString(),
        createdAt: new Date().toISOString(),
      }));

      await updateDoc(docRef, {
        schedules: [...existingSchedules, ...newSchedules],
      });

      alert("Schedules saved!");
      navigate("/mylistings");
    } catch (err) {
      console.error("Save failed:", err);
      alert("Failed to save.");
    } finally {
      setLoading(false);
    }
  };

  const deletePendingSchedule = (dateToRemove) => {
    setPendingSchedules((prev) => prev.filter((d) => d.toISOString() !== dateToRemove.toISOString()));
  };

  const deleteExistingSchedule = async (index) => {
    try {
      const updated = [...existingSchedules];
      updated.splice(index, 1);
      setExistingSchedules(updated);
      await updateDoc(doc(db, "properties", id), { schedules: updated });
    } catch (err) {
      console.error("Delete from Firestore failed:", err);
    }
  };

  const generateHours = () => {
    const hours = [];
    for (let i = 1; i <= 12; i++) {
      hours.push(i.toString().padStart(2, "0"));
    }
    return hours;
  };

  const generateMinutes = () => {
    const minutes = [];
    for (let i = 0; i < 60; i += 15) {
      minutes.push(i.toString().padStart(2, "0"));
    }
    return minutes;
  };

  const hours = generateHours();
  const minutes = generateMinutes();

  const handleAddSchedule = () => {
    if (!selectedDate || !selectedHour || !selectedMinute || !selectedAmPm) {
      alert("Please select date, hour, minute, and AM/PM.");
      return;
    }

    // Convert 12-hour format to 24-hour format
    let hour24 = parseInt(selectedHour);
    if (selectedAmPm === "PM" && hour24 !== 12) {
      hour24 += 12;
    } else if (selectedAmPm === "AM" && hour24 === 12) {
      hour24 = 0;
    }

    const combinedDateTime = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), hour24, parseInt(selectedMinute));

    // Only one schedule per day — replace if needed
    const dayStr = combinedDateTime.toISOString().split("T")[0];
    const exists = pendingSchedules.some((d) => d.toISOString().split("T")[0] === dayStr);

    if (exists) {
      setPendingSchedules((prev) => prev.map((d) => (d.toISOString().split("T")[0] === dayStr ? combinedDateTime : d)));
    } else {
      setPendingSchedules((prev) => [...prev, combinedDateTime]);
    }

    // Reset selections
    setSelectedHour("");
    setSelectedMinute("");
    setSelectedAmPm("");
  };

  // Show loading spinner while checking authentication and authorization
  if (authLoading || pageLoading) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-lg text-gray-600">Loading...</p>
          </div>
        </div>
      </>
    );
  }

  // If user is not authenticated or not authorized, this component will redirect
  // But we can add a fallback just in case
  if (!user) {
    return null;
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8 px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Schedule Your Visit</h1>
          </div>

          {/* Main Content Card */}
          <div className="bg-white rounded-3xl shadow-2xl p-6 md:p-8 mb-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calendar Section */}
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6 rounded-2xl">
                  <h2 className="text-2xl font-semibold mb-2">Choose Date</h2>
                  <p className="text-blue-100">Select a date from the calendar below</p>
                </div>

                <div className="flex justify-center">
                  <div className="overflow-hidden transition-shadow duration-300">
                    <DatePicker
                      selected={selectedDate}
                      onChange={(date) => {
                        setSelectedDate(date);
                        setSelectedHour("");
                        setSelectedMinute("");
                        setSelectedAmPm("");
                      }}
                      minDate={new Date()}
                      inline
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Time Selection Section */}
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6 rounded-2xl">
                  <h2 className="text-2xl font-semibold mb-2">Select Time</h2>
                  <p className="text-purple-100">{selectedDate ? `Choose a time for ${selectedDate.toLocaleDateString()}` : "Please select a date first"}</p>
                </div>

                {selectedDate ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      {/* Hour Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Hour</label>
                        <select
                          value={selectedHour}
                          onChange={(e) => setSelectedHour(e.target.value)}
                          className="w-full p-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                        >
                          <option value="">--</option>
                          {hours.map((hour) => (
                            <option key={hour} value={hour}>
                              {hour}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Minute Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Minute</label>
                        <select
                          value={selectedMinute}
                          onChange={(e) => setSelectedMinute(e.target.value)}
                          className="w-full p-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                        >
                          <option value="">--</option>
                          {minutes.map((minute) => (
                            <option key={minute} value={minute}>
                              {minute}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* AM/PM Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">AM/PM</label>
                        <select
                          value={selectedAmPm}
                          onChange={(e) => setSelectedAmPm(e.target.value)}
                          className="w-full p-3 border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                        >
                          <option value="">--</option>
                          <option value="AM">AM</option>
                          <option value="PM">PM</option>
                        </select>
                      </div>
                    </div>

                    <button
                      onClick={handleAddSchedule}
                      disabled={!selectedHour || !selectedMinute || !selectedAmPm}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      Add Schedule
                    </button>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-2xl p-12 text-center">
                    <div className="text-gray-400 mb-4">
                      <svg className="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-gray-500 text-lg">Select a date to choose a time</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Schedules Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Pending Schedules */}
            {pendingSchedules.length > 0 && (
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-orange-100 p-3 rounded-full mr-4">
                    <svg className="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">New Schedules</h3>
                </div>
                <div className="space-y-3">
                  {pendingSchedules.map((date, idx) => (
                    <div key={idx} className="flex justify-between items-center p-4 bg-orange-50 rounded-xl border border-orange-200">
                      <div className="flex items-center">
                        <span className="text-orange-600 mr-3 text-xl">📌</span>
                        <span className="font-medium text-gray-800">{date.toLocaleString()}</span>
                      </div>
                      <button onClick={() => deletePendingSchedule(date)} className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2 rounded-lg transition-colors duration-200">
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Existing Schedules */}
            {existingSchedules.length > 0 && (
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-green-100 p-3 rounded-full mr-4">
                    <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Confirmed Schedules</h3>
                </div>
                <div className="space-y-3">
                  {existingSchedules.map((s, idx) => (
                    <div key={idx} className="flex justify-between items-center p-4 bg-green-50 rounded-xl border border-green-200">
                      <div className="flex items-center">
                        <span className="text-green-600 mr-3 text-xl">✅</span>
                        <span className="font-medium text-gray-800">{new Date(s.date).toLocaleString()}</span>
                      </div>
                      <button onClick={() => deleteExistingSchedule(idx)} className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2 rounded-lg transition-colors duration-200">
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <div className="mt-8 flex justify-center">
            <button
              onClick={handleSaveAll}
              disabled={loading}
              className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white font-semibold py-4 px-8 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none min-w-[200px]"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </div>
              ) : (
                "Save Selected Schedules"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
