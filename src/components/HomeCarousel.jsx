import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { collection, getDocs } from "firebase/firestore";
import { db } from "../firebase"; // adjust this to your firebase config file
import { Heart, ChevronLeft, ChevronRight } from "lucide-react";
import { useAuth } from "../context/AuthProvider"; // assuming you have an AuthProvider

export default function HomeCarousel() {
  const [properties, setProperties] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [favorites, setFavorites] = useState(new Set());
  const { user } = useAuth(); // assuming you have user context

  useEffect(() => {
    if (user) {
      const fetchProperties = async () => {
        const querySnapshot = await getDocs(collection(db, "properties"));
        const data = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setProperties(data);
      };
      fetchProperties();
    }
  }, [user]);

  const itemsPerView = 4;
  const maxIndex = Math.max(0, properties.length - itemsPerView);

  const nextSlide = () => {
    setCurrentIndex((prev) => Math.min(prev + 1, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => Math.max(prev - 1, 0));
  };

  const toggleFavorite = (id) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(id)) {
        newFavorites.delete(id);
      } else {
        newFavorites.add(id);
      }
      return newFavorites;
    });
  };

  const getPropertyTag = (home) => {
    if (home.featured) return { tag: "Showcase", color: "bg-orange-500" };
    if (home.daysOnMarket && home.daysOnMarket <= 7)
      return {
        tag: `${home.daysOnMarket} days on Market`,
        color: "bg-red-500",
      };
    if (home.specialFeature) return { tag: home.specialFeature, color: "bg-orange-500" };
    return null;
  };
function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }
  return (
    <div className="py-8 px-4 bg-white" data-aos="fade-up" data-aos-duration="1000" data-aos-offset="200">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-1">Homes For You</h2>
            <p className="text-gray-600 text-sm">Based on homes you recently viewed</p>
          </div>

          {/* Navigation arrows */}
          <div className="flex gap-2">
            <button
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className={`p-2 rounded-full border transition-colors ${
                currentIndex === 0 ? "border-gray-200 text-gray-300 cursor-not-allowed" : "border-gray-300 text-gray-600 hover:bg-gray-50"
              }`}
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={nextSlide}
              disabled={currentIndex >= maxIndex}
              className={`p-2 rounded-full border transition-colors ${
                currentIndex >= maxIndex ? "border-gray-200 text-gray-300 cursor-not-allowed" : "border-gray-300 text-gray-600 hover:bg-gray-50"
              }`}
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>

        {/* Carousel */}
        <div className="overflow-hidden h-80">
          <div
            className="flex transition-transform duration-300 ease-in-out gap-4"
            style={{
              transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
            }}
          >
            {properties.map((home) => {
              const propertyTag = getPropertyTag(home);
              return (
                <div key={home.id} className="flex-none w-1/4 bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <Link to={`/property/${home.id}`}>
                    {/* Image container */}
                    <div className="relative">
                      <img
                        src={home.imageUrls?.[0] || "https://via.placeholder.com/300x200"}
                        alt="property"
                        className="w-full h-40 object-cover"
                      />

                      {/* Tag overlay */}
                      {propertyTag && (
                        <div className={`absolute top-3 left-3 ${propertyTag.color} text-white text-xs px-2 py-1 rounded`}>
                          {propertyTag.tag}
                        </div>
                      )}

                      {/* Heart icon */}
                      {/* <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          toggleFavorite(home.id);
                        }}
                        className="absolute top-3 right-3 p-1.5 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow"
                      >
                        <Heart size={16} className={favorites.has(home.id) ? "fill-red-500 text-red-500" : "text-gray-600"} />
                      </button> */}
                    </div>

                    {/* Content */}
                    <div className="p-4">
                      <h3 className="text-xl font-bold text-gray-900 mb-1">
                        €{formatPrice(home.salePrice)|| "—"}
                      </h3>
                      <div className="flex items-center text-gray-600 text-sm mb-2">
                        {home.bedrooms && <span>{home.bedrooms} bds</span>}
                        {home.bedrooms && home.bathrooms && <span className="mx-1">|</span>}
                        {home.bathrooms && <span>{home.bathrooms} ba</span>}
                        {(home.bedrooms || home.bathrooms) && home.areaSize && <span className="mx-1">|</span>}
                        {home.areaSize && <span>{home.areaSize} m<sup>2</sup></span>}
                        {(home.bedrooms || home.bathrooms || home.areaSize) && <span className="mx-1">|</span>}
                        <span>Active</span>
                      </div>
                      <p className="text-gray-700 text-sm leading-relaxed">{home.address}</p>
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
