import React, { useEffect, useState } from "react";
import { db, storage } from "../firebase";
import { collection, query, where, getDoc, getDocs, doc, deleteDoc, updateDoc } from "firebase/firestore";
import { ref, deleteObject, uploadBytes, getDownloadURL } from "firebase/storage";
import { useAuth } from "../context/AuthProvider";
import { FaEdit, FaTrashAlt, FaTimes, FaBed, FaBath, FaCar, FaHome, FaEye, FaCalendarAlt } from "react-icons/fa";
import { X, MapPin, Euro, Square, Star } from "lucide-react";
import Header from "./Header";
import { Link, useNavigate } from "react-router-dom";
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import PropertiesWithViewings from "./PropertiesWithViewings";

export default function MyListings() {
  const { user } = useAuth();
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(null);
  const [formData, setFormData] = useState({});
  const [expandedCards, setExpandedCards] = useState({});
  const [deletingIndex, setDeletingIndex] = useState(null);
  const [activeTab, setActiveTab] = useState("For Sale");
  const [uploading, setUploading] = useState(false);

  const navigate = useNavigate();

  const sensors = useSensors(useSensor(PointerSensor));

  useEffect(() => {
    const fetchListings = async () => {
      if (!user?.email) return;
      try {
        const q = query(collection(db, "properties"), where("agentEmail", "==", user.email));
        const snapshot = await getDocs(q);
        const results = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setListings(results);
      } catch (err) {
        console.error("Error fetching listings:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchListings();
  }, [user]);

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this listing?")) return;

    try {
      const propertyRef = doc(db, "properties", id);
      const propertySnap = await getDoc(propertyRef);

      if (propertySnap.exists()) {
        const propertyData = propertySnap.data();

        // Delete all images
        if (Array.isArray(propertyData.imageUrls)) {
          for (const url of propertyData.imageUrls) {
            try {
              const decodedPath = decodeURIComponent(url.split("/o/")[1].split("?")[0]);
              const fileRef = ref(storage, decodedPath);
              await deleteObject(fileRef);
            } catch (err) {
              console.error("Failed to delete image from storage:", err);
            }
          }
        }

        // Delete layoutImageUrl if exists
        if (propertyData.layoutImageUrl) {
          try {
            const decodedPath = decodeURIComponent(propertyData.layoutImageUrl.split("/o/")[1].split("?")[0]);
            const fileRef = ref(storage, decodedPath);
            await deleteObject(fileRef);
          } catch (err) {
            console.error("Failed to delete layout image:", err);
          }
        }

        // Delete videoUrl if exists
        if (propertyData.videoUrl) {
          try {
            const decodedPath = decodeURIComponent(propertyData.videoUrl.split("/o/")[1].split("?")[0]);
            const fileRef = ref(storage, decodedPath);
            await deleteObject(fileRef);
          } catch (err) {
            console.error("Failed to delete video file:", err);
          }
        }
      }

      // Finally delete the Firestore document
      await deleteDoc(propertyRef);
      setListings((prev) => prev.filter((item) => item.id !== id));
    } catch (err) {
      console.error("Failed to delete property:", err);
    }
  };

  const openEditForm = (property) => {
    setEditing(property.id);
    setFormData({ ...property, id: property.id });
  };

  const closeEditForm = () => {
    setEditing(null);
    setFormData({});
    setDeletingIndex(null);
  };

  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((f) => ({
      ...f,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSave = async () => {
    if (uploading) {
      alert("Please wait for uploads to complete before saving.");
      return;
    }

    try {
      const updatedData = { ...formData };
      delete updatedData.id;
      await updateDoc(doc(db, "properties", editing), updatedData);
      setListings((prev) => prev.map((item) => (item.id === editing ? { id: editing, ...updatedData } : item)));
      closeEditForm();
    } catch (err) {
      console.error("Update failed:", err);
    }
  };

  const toggleExpand = (id) => {
    setExpandedCards((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const deleteImageFromFormData = async (index) => {
    const imageToDelete = formData.imageUrls[index];
    try {
      const fileRef = ref(storage, imageToDelete);
      await deleteObject(fileRef);

      const updatedImages = [...formData.imageUrls];
      updatedImages.splice(index, 1);
      setFormData((prev) => ({ ...prev, imageUrls: updatedImages }));

      const docId = formData.id || editing;
      if (docId) {
        const docRef = doc(db, "properties", docId);
        await updateDoc(docRef, { imageUrls: updatedImages });
      } else {
        console.error("No document ID found for update");
      }
    } catch (err) {
      console.error("Error deleting image:", err);
    }
  };

  const uploadNewImages = async (files) => {
    setUploading(true);
    try {
      const docId = formData.id || editing;
      if (!docId) throw new Error("No document ID to associate uploads");

      const uploads = files.map(async (file) => {
        const fileRef = ref(storage, `properties/${docId}/${Date.now()}_${file.name}`);
        await uploadBytes(fileRef, file);
        return await getDownloadURL(fileRef);
      });

      const newUrls = await Promise.all(uploads);
      const combinedUrls = [...(formData.imageUrls || []), ...newUrls];
      setFormData((prev) => ({ ...prev, imageUrls: combinedUrls }));

      const docRef = doc(db, "properties", docId);
      await updateDoc(docRef, { imageUrls: combinedUrls });
    } catch (err) {
      console.error("Failed to upload images or update Firestore:", err);
    } finally {
      setUploading(false);
    }
  };

  // Updated SortableImage component with bookmark for cover image
  const SortableImage = ({ url, index, onDelete }) => {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: url });
    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };
    const isDeleting = deletingIndex === index;
    const isCoverImage = index === 0; // First image is cover image

    const handleDelete = async (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (isDeleting) return; // Prevent multiple clicks

      setDeletingIndex(index);
      try {
        await onDelete(index);
      } finally {
        setDeletingIndex(null);
      }
    };

    return (
      <div ref={setNodeRef} style={style} className={`relative w-24 h-24 rounded-lg overflow-hidden transition-opacity duration-300 ease-in-out ${isDeleting ? "opacity-50" : "opacity-100"}`}>
        {/* Draggable area - exclude the button */}
        <div className="absolute inset-0 cursor-move" {...attributes} {...listeners} />

        {isDeleting && <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white text-xs z-30">Deleting...</div>}

        <img src={url} alt={`img-${index}`} className="w-full h-full object-cover" />

        {/* Cover Image Bookmark */}
        {isCoverImage && (
          <div className="absolute top-1 left-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center z-20">
            <Star size={10} className="mr-1" />
            Cover
          </div>
        )}

        {/* Delete button with higher z-index and no drag listeners */}
        <button
          type="button"
          className="absolute top-0 right-0 w-6 h-6 flex items-center justify-center bg-red-500 text-white rounded-full z-40 hover:bg-red-600 transition-colors cursor-pointer"
          onClick={handleDelete}
          disabled={isDeleting}
          style={{ pointerEvents: isDeleting ? "none" : "auto" }}
        >
          <X size={16} />
        </button>
      </div>
    );
  };

  // Updated DndContext onDragEnd handler
  <DndContext
    sensors={sensors}
    collisionDetection={closestCenter}
    onDragEnd={({ active, over }) => {
      if (!over || active.id === over.id) return;
      const oldIndex = formData.imageUrls.findIndex((url) => url === active.id);
      const newIndex = formData.imageUrls.findIndex((url) => url === over.id);
      const reordered = arrayMove(formData.imageUrls, oldIndex, newIndex);
      setFormData((prev) => ({ ...prev, imageUrls: reordered }));

      // Update the document in Firestore with new image order
      const docId = formData.id || editing;
      if (docId) {
        updateDoc(doc(db, "properties", docId), { imageUrls: reordered });
      }
    }}
  />;

  const filteredListings = listings.filter((property) => {
    if (activeTab === "For Sale") return property.saleOrRent && !property.isSold && !property.saleAgreed;
    if (activeTab === "For Rent") return !property.saleOrRent && !property.isSold && !property.saleAgreed;
    if (activeTab === "sold") return property.isSold === true;
    if (activeTab === "sale agreed") return property.saleAgreed === true;
    return true;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }
  function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }
  return (
    <div className="min-h-screen bg-white-50">
      <Header />
      {/* Tab Navigation */}
      <div className="sticky top-18 bg-white  z-40">
        <div className="container mx-auto px-4">
          <div className="flex justify-center space-x-1 py-4">
            {["For Sale", "For Rent", "sale agreed", "sold", "viewings"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeTab === tab ? "bg-blue-600 text-white shadow-lg transform scale-105" : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:transform hover:scale-105"
                }`}
              >
                {tab === "viewings" ? "📅 Viewings" : `${tab.charAt(0).toUpperCase() + tab.slice(1)}`}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 mt-15">
        {activeTab === "viewings" ? (
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <FaCalendarAlt className="mr-3 text-blue-600" />
              Scheduled Viewings
            </h2>
            <PropertiesWithViewings />
          </div>
        ) : (
          <>
            <div className="bg-white p-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">Your Listings</h2>
            </div>
            {filteredListings.length === 0 ? (
              <div className="text-center py-20">
                <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
                  <FaHome className="text-6xl text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">No Properties Found</h3>
                  <p className="text-gray-600">You don't have any properties in this category yet.</p>
                </div>
              </div>
            ) : (
              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {filteredListings.map((property) => {
                  const isExpanded = expandedCards[property.id] || false;
                  return (
                    <div key={property.id} className="bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:transform hover:scale-105 group">
                      {/* Property Image */}
                      <div className="relative h-35 overflow-hidden">
                        <img
                          src={property.imageUrls?.[0] || "https://via.placeholder.com/400x300"}
                          alt="Property"
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                        />
                        {property.isFeatured && (
                          <div className="absolute top-3 left-3 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-semibold flex items-center">
                            <Star size={12} className="mr-1" />
                            Featured
                          </div>
                        )}
                        {property.isSold && <div className="absolute top-3 right-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold">SOLD</div>}
                      </div>

                      {/* Property Info */}
                      <div className="p-5">
                        <div className="mb-3">
                          <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{property.address}</h3>
                          <div className="flex items-center text-blue-600 font-bold text-xl mb-2">
                            <Euro size={20} className="mr-1" />
                            {formatPrice(property.salePrice)}
                          </div>
                        </div>
                        {/* Action Buttons */}
                        <div className="flex justify-between items-center pt-3">
                          <Link to={`/property/${property.id}`} className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <FaEye className="mr-1" />
                            View
                          </Link>

                          <div className="flex space-x-2">
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                navigate(`/schedule/${property.id}`);
                              }}
                              className="text-green-600 hover:text-green-800 text-sm p-2 rounded-full hover:bg-green-50 transition-colors"
                              title="Schedule Viewing"
                            >
                              <FaCalendarAlt />
                            </button>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                openEditForm(property);
                              }}
                              className="text-blue-600 hover:text-blue-800 text-sm p-2 rounded-full hover:bg-blue-50 transition-colors"
                              title="Edit Property"
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                handleDelete(property.id);
                              }}
                              className="text-red-500 hover:text-red-700 text-sm p-2 rounded-full hover:bg-red-50 transition-colors"
                              title="Delete Property"
                            >
                              <FaTrashAlt />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}

        {/* Edit Modal */}
        {editing && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white w-full max-w-4xl rounded-2xl shadow-2xl relative max-h-[90vh] flex flex-col">
              {/* Modal Header */}
              <div className="flex-shrink-0 bg-white border-b p-6 rounded-t-2xl">
                <div className="flex items-center justify-between">
                  <h3 className="text-2xl font-bold text-gray-800">Edit Property</h3>
                  <button onClick={closeEditForm} className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <FaTimes size={20} />
                  </button>
                </div>
              </div>

              {/* Modal Content - Scrollable */}
              <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { field: "address", label: "Address", type: "text" },
                    { field: "salePrice", label: "Sale Price", type: "number" },
                    { field: "areaSize", label: "Area Size", type: "text" },
                    { field: "bedrooms", label: "Bedrooms", type: "number" },
                    { field: "bathrooms", label: "Bathrooms", type: "number" },
                    { field: "berRating", label: "BER Rating", type: "text" },
                    { field: "eirCode", label: "EIR Code", type: "text" },
                    { field: "propertyType", label: "Property Type", type: "text" },
                    { field: "mgmt", label: "Management Charges", type: "text" },
                    { field: "parkingSpaces", label: "Parking Spaces", type: "number" },
                  ].map(({ field, label, type }) => (
                    <div key={field} className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">{label}</label>
                      <input
                        type={type}
                        name={field}
                        value={formData[field] || ""}
                        onChange={handleFormChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                        placeholder={`Enter ${label.toLowerCase()}`}
                      />
                    </div>
                  ))}
                </div>

                {/* USP - Full Width */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Unique Selling Points</label>
                  <textarea
                    name="usp"
                    value={formData.usp || ""}
                    onChange={handleFormChange}
                    rows="3"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    placeholder="Enter unique selling points..."
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    name="description"
                    value={formData.description || ""}
                    onChange={handleFormChange}
                    rows="3"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    placeholder="Enter unique selling points..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <input type="checkbox" name="isFeatured" checked={formData.isFeatured || false} onChange={handleFormChange} className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500" />
                    <span className="text-sm font-medium text-gray-700">Featured Listing</span>
                  </label>
                  <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <input
                      type="checkbox"
                      name="isSold"
                      checked={formData.isSold === true}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData((prev) => ({ ...prev, isSold: true, saleAgreed: false }));
                        } else {
                          setFormData((prev) => ({ ...prev, isSold: false }));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Mark as Sold</span>
                  </label>
                  <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <input
                      type="checkbox"
                      name="saleAgreed"
                      checked={formData.saleAgreed === true}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData((prev) => ({ ...prev, saleAgreed: true, isSold: false }));
                        } else {
                          setFormData((prev) => ({ ...prev, saleAgreed: false }));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Sale Agreed</span>
                  </label>
                  <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <input
                      type="checkbox"
                      name="saleOrRent"
                      checked={formData.saleOrRent === true}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData((prev) => ({ ...prev, saleOrRent: true }));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">For Sale</span>
                  </label>
                  <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <input
                      type="checkbox"
                      name="saleOrRent"
                      checked={formData.saleOrRent === false}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData((prev) => ({ ...prev, saleOrRent: false }));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">For Rent</span>
                  </label>
                </div>

                {/* Image Management */}
                <div className="space-y-6">
                  {/* Property Photos */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">Property Photos</h4>
                    <div className="w-full overflow-hidden">
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={({ active, over }) => {
                          if (!over || active.id === over.id) return;
                          const oldIndex = formData.imageUrls.findIndex((url) => url === active.id);
                          const newIndex = formData.imageUrls.findIndex((url) => url === over.id);
                          const reordered = arrayMove(formData.imageUrls, oldIndex, newIndex);
                          setFormData((prev) => ({ ...prev, imageUrls: reordered }));
                        }}
                      >
                        <SortableContext items={formData.imageUrls || []} strategy={verticalListSortingStrategy}>
                          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 mb-4">
                            {(formData.imageUrls || []).map((url, idx) => (
                              <SortableImage key={url} url={url} index={idx} onDelete={deleteImageFromFormData} />
                            ))}
                          </div>
                        </SortableContext>
                      </DndContext>
                    </div>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) uploadNewImages(files);
                      }}
                      className="w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                  </div>

                  {/* Layout Image */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">Layout Image</h4>
                    <div className="w-full">
                      {formData.layoutImageUrl ? (
                        <div className="relative inline-block max-w-full">
                          <img src={formData.layoutImageUrl} alt="Layout" className="max-w-full h-auto max-h-48 object-contain rounded-lg shadow-md" />
                          <button
                            type="button"
                            className="absolute -top-2 -right-2 w-8 h-8 flex items-center justify-center bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors disabled:opacity-50"
                            onClick={async (e) => {
                              e.preventDefault();
                              e.stopPropagation();

                              const button = e.currentTarget;
                              button.disabled = true;

                              const docId = formData.id || editing;
                              if (!formData.layoutImageUrl || !docId) {
                                button.disabled = false;
                                return;
                              }

                              try {
                                const url = formData.layoutImageUrl;
                                const decodedPath = decodeURIComponent(url.split("/o/")[1].split("?")[0]);
                                const fileRef = ref(storage, decodedPath);
                                await deleteObject(fileRef);

                                setFormData((prev) => ({ ...prev, layoutImageUrl: "" }));
                                await updateDoc(doc(db, "properties", docId), { layoutImageUrl: "" });
                              } catch (err) {
                                console.error("Failed to delete layout image:", err);
                              } finally {
                                button.disabled = false;
                              }
                            }}
                          >
                            <X size={16} />
                          </button>
                        </div>
                      ) : (
                        <input
                          type="file"
                          accept="image/*"
                          onChange={async (e) => {
                            const file = e.target.files[0];
                            const docId = formData.id || editing;
                            if (!file || !docId) return;

                            setUploading(true);
                            try {
                              const fileRef = ref(storage, `properties/${docId}/layout_${Date.now()}_${file.name}`);
                              await uploadBytes(fileRef, file);
                              const url = await getDownloadURL(fileRef);

                              setFormData((prev) => ({ ...prev, layoutImageUrl: url }));
                              const docRef = doc(db, "properties", docId);
                              await updateDoc(docRef, { layoutImageUrl: url });
                            } catch (err) {
                              console.error("Error uploading layout image:", err);
                            } finally {
                              setUploading(false);
                            }
                          }}
                          className="w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                      )}
                    </div>
                  </div>

                  {/* Video Upload */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">Property Video</h4>
                    <div className="w-full">
                      {formData.videoUrl ? (
                        <div className="relative inline-block max-w-full">
                          <video src={formData.videoUrl} controls className="max-w-full h-auto max-h-48 rounded-lg shadow-md" />
                          <button
                            type="button"
                            className="absolute -top-2 -right-2 w-8 h-8 flex items-center justify-center bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors disabled:opacity-50"
                            onClick={async (e) => {
                              e.preventDefault();
                              e.stopPropagation();

                              const button = e.currentTarget;
                              button.disabled = true;

                              const docId = formData.id || editing;
                              if (!formData.videoUrl || !docId) {
                                button.disabled = false;
                                return;
                              }

                              try {
                                const url = formData.videoUrl;
                                const decodedPath = decodeURIComponent(url.split("/o/")[1].split("?")[0]);
                                const fileRef = ref(storage, decodedPath);
                                await deleteObject(fileRef);

                                setFormData((prev) => ({ ...prev, videoUrl: "" }));
                                await updateDoc(doc(db, "properties", docId), { videoUrl: "" });
                              } catch (err) {
                                console.error("Failed to delete video:", err);
                              } finally {
                                button.disabled = false;
                              }
                            }}
                          >
                            <X size={16} />
                          </button>
                        </div>
                      ) : (
                        <input
                          type="file"
                          accept="video/*"
                          onChange={async (e) => {
                            const file = e.target.files[0];
                            const docId = formData.id || editing;
                            if (!file || !docId) return;

                            setUploading(true);
                            try {
                              const fileRef = ref(storage, `properties/${docId}/video_${Date.now()}_${file.name}`);
                              await uploadBytes(fileRef, file);
                              const url = await getDownloadURL(fileRef);

                              setFormData((prev) => ({ ...prev, videoUrl: url }));
                              await updateDoc(doc(db, "properties", docId), { videoUrl: url });
                            } catch (err) {
                              console.error("Error uploading video:", err);
                            } finally {
                              setUploading(false);
                            }
                          }}
                          className="w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Save Button - Fixed at bottom */}
              <div className="flex-shrink-0 flex justify-end p-6 border-t bg-white rounded-b-2xl">
                <button
                  onClick={handleSave}
                  disabled={uploading}
                  className={`px-8 py-3 rounded-lg font-semibold shadow-lg transition-colors ${
                    uploading ? "bg-gray-400 text-gray-600 cursor-not-allowed" : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                >
                  {uploading ? "Uploading..." : "Save Changes"}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
