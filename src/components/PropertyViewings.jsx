import React, { useEffect, useState } from "react";
import { db } from "../firebase";
import { collection, query, where, getDocs, doc, getDoc } from "firebase/firestore";
import { useAuth } from "../context/AuthProvider";
import { useParams, useNavigate } from "react-router-dom";
import { FaArrowLeft, FaCalendarAlt, FaHome } from "react-icons/fa";
import { Euro, MapPin } from "lucide-react";
import Header from "./Header";

export default function PropertyViewings() {
  const { propertyId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [property, setProperty] = useState(null);
  const [viewings, setViewings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    const fetchPropertyAndViewings = async () => {
      // Check if user is authenticated
      if (!user?.email || !propertyId) {
        navigate("/");
        return;
      }

      try {
        // Fetch property details first to verify ownership
        const propertyRef = doc(db, "properties", propertyId);
        const propertySnap = await getDoc(propertyRef);

        if (propertySnap.exists()) {
          const propertyData = { id: propertySnap.id, ...propertySnap.data() };

          // Check if current user is the agent for this property
          if (propertyData.agentEmail !== user.email) {
            // Unauthorized access - redirect to home
            navigate("/");
            return;
          }

          // User is authorized
          setProperty(propertyData);
          setAuthorized(true);

          // Now fetch viewings for this property and agent
          const viewingsQuery = query(
            collection(db, "viewings"),
            where("propertyId", "==", propertyId),
            where("agentEmail", "==", user.email)
          );
          const viewingsSnapshot = await getDocs(viewingsQuery);
          const viewingsData = viewingsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }));

          // Sort viewings by date (oldest first - ascending order)
          viewingsData.sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate));
          setViewings(viewingsData);
        } else {
          // Property doesn't exist - redirect to home
          navigate("/");
          return;
        }
      } catch (err) {
        console.error("Failed to fetch property and viewings:", err);
        // On error, redirect to home for security
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    fetchPropertyAndViewings();
  }, [user, propertyId, navigate]);

  // Show loading while checking authentication and fetching data
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading property viewings...</p>
          </div>
        </div>
      </div>
    );
  }

  // If not authorized or no property found, this will be handled by the redirect in useEffect
  // But adding a fallback just in case
  if (!authorized || !property || !user?.email) {
    return null;
  }

  function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="container mx-auto px-4 py-8">
        {/* Header with Back Button */}
        <div className="flex items-center mb-8">
          <button onClick={() => navigate("/mylistings")} className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors mr-4">
            <FaArrowLeft className="mr-2" />
            Back to Listings
          </button>
          <div className="w-px h-6 bg-gray-300 mr-4"></div>
          <h1 className="text-3xl font-bold text-gray-800">Property Viewings</h1>
        </div>

        {/* Property Summary Card */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-1/3">
              <img src={property.imageUrls?.[0] || "https://via.placeholder.com/400x300"} alt="Property" className="w-full h-48 object-cover rounded-lg" />
            </div>
            <div className="md:w-2/3">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">{property.address}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center text-blue-600 font-bold text-xl">
                  <Euro size={20} className="mr-1" />
                  {formatPrice(property.salePrice)}
                </div>
                <div className="flex items-center text-gray-600">
                  <MapPin size={16} className="mr-1" />
                  {property.eirCode}
                </div>
                <div className="text-gray-600">
                  <span className="font-medium">Type:</span> {property.propertyType}
                </div>
                <div className="text-gray-600">
                  <span className="font-medium">Status:</span> {property.saleOrRent ? "For Sale" : "For Rent"}
                </div>
              </div>
              <div className="mt-4 px-4 py-2 bg-blue-50 rounded-lg inline-block">
                <span className="text-blue-800 font-medium">
                  {viewings.length} Viewing{viewings.length !== 1 ? "s" : ""} Scheduled
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Viewings List */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <FaCalendarAlt className="mr-3 text-blue-600" />
            Scheduled Viewings
          </h3>

          {viewings.length === 0 ? (
            <div className="text-center py-12">
              <FaCalendarAlt className="text-6xl text-gray-300 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-gray-800 mb-2">No Viewings Scheduled</h4>
              <p className="text-gray-600">This property doesn't have any scheduled viewings yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {viewings.map((viewing) => (
                <div key={viewing.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 text-lg">{viewing.userEmail}</h4>
                          <div className="flex items-center text-purple-600 font-medium">
                            <FaCalendarAlt className="mr-1" />
                            {new Date(viewing.scheduledDate).toLocaleString("en-IN", {
                              dateStyle: "full",
                              timeStyle: "short",
                              timeZone: "Asia/Kolkata",
                            })}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {viewing.phoneFull && (
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                />
                              </svg>
                            </div>
                            <span className="text-gray-700">{viewing.phoneFull}</span>
                          </div>
                        )}

                        {viewing.whatsappFull && (
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                              <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700" />
                              </svg>
                            </div>
                            <span className="text-gray-700">{viewing.whatsappFull}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
