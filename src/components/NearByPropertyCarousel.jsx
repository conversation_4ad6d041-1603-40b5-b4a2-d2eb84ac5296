import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { collection, getDocs } from "firebase/firestore";
import { db } from "../firebase";
import { ChevronLeft, ChevronRight, Euro } from "lucide-react";
import { Loader } from "@googlemaps/js-api-loader";

function getDistanceFromLatLonInKm(lat1, lon1, lat2, lon2) {
  const R = 6371;
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a = Math.sin(dLat / 2) ** 2 + Math.cos((lat1 * Math.PI) / 180) * Math.cos((lat2 * Math.PI) / 180) * Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export default function NearByPropertyCarousel({ currentAddress }) {
  const [properties, setProperties] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const itemsPerView = 4;

  useEffect(() => {
    const loadNearbyProperties = async () => {
      const loader = new Loader({
        apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
        libraries: ["places"],
      });

      await loader.load();

      const geocoder = new google.maps.Geocoder();

      const geocode = (address) =>
        new Promise((resolve, reject) => {
          geocoder.geocode({ address }, (results, status) => {
            if (status === "OK" && results[0]) {
              resolve(results[0].geometry.location);
            } else {
              reject(`Geocode failed for ${address}: ${status}`);
            }
          });
        });

      try {
        const currentLocation = await geocode(currentAddress);

        const querySnapshot = await getDocs(collection(db, "properties"));
        const allProperties = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));

        const filtered = [];

        for (const property of allProperties) {
          if (property.address && property.address !== currentAddress) {
            try {
              const loc = await geocode(property.address);
              const distance = getDistanceFromLatLonInKm(currentLocation.lat(), currentLocation.lng(), loc.lat(), loc.lng());

              if (distance <= 3) {
                filtered.push(property);
              }
            } catch (e) {
              console.warn("Geocode failed:", e);
            }
          }
        }

        setProperties(filtered);
      } catch (error) {
        console.error("Failed to load nearby properties:", error);
      }
    };

    if (currentAddress) {
      loadNearbyProperties();
    }
  }, [currentAddress]);

  const maxIndex = Math.max(0, properties.length - itemsPerView);
  const nextSlide = () => setCurrentIndex((prev) => Math.min(prev + 1, maxIndex));
  const prevSlide = () => setCurrentIndex((prev) => Math.max(prev - 1, 0));
  function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }
  return (
    <div className="py-8 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Nearby Properties</h2>
          <div className="flex gap-2">
            <button onClick={prevSlide} disabled={currentIndex === 0} className="p-2 rounded-full border">
              <ChevronLeft size={20} />
            </button>
            <button onClick={nextSlide} disabled={currentIndex >= maxIndex} className="p-2 rounded-full border">
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
        {properties.length === 0 ? (
          <div className="text-center text-gray-500 py-10 text-lg font-medium">No nearby listings found.</div>
        ) : (
          <div className="overflow-hidden h-80">
            <div
              className="flex transition-transform duration-300 ease-in-out gap-4"
              style={{
                transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
              }}
            >
              {properties.map((home) => (
                <div key={home.id} className="flex-none w-1/4 bg-white rounded-lg overflow-hidden shadow hover:shadow-lg transition-shadow">
                  <Link to={`/property/${home.id}`}>
                    <img src={home.imageUrls?.[0] || "https://via.placeholder.com/300x200"} alt="property" className="w-full h-40 object-cover" />
                    <div className="p-4">
                      <div className="flex items-center text-blue-600 font-bold text-xl">
                        <Euro size={20} className="mr-1" />
                        {formatPrice(home.salePrice)}
                      </div>
                      <div className="text-gray-600 text-sm mb-2">
                        {home.bedrooms} bds | {home.bathrooms} ba | {home.areaSize} m²
                      </div>
                      <p className="text-gray-700 text-sm">{home.address}</p>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
