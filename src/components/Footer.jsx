import React, { Component } from "react";

export default class Footer extends Component {
  render() {
    return (
      <footer className="bg-gray-800 text-gray-400 py-8 text-center space-y-4">
        <div className="flex flex-wrap justify-center gap-6 text-sm">
          {["About", "Zestimates", "Research", "Careers", "Privacy", "Help", "Advertise", "Terms of use", "Fair Housing Guide"].map((link) => (
            <a key={link} href="#" className="hover:text-white">
              {link}
            </a>
          ))}
        </div>
        <div className="text-xs">&copy; {new Date().getFullYear()} Finf My GAFF, Inc #29122002.</div>
      </footer>
    );
  }
}
