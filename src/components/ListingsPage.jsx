import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { collection, getDocs, query, doc, deleteDoc, addDoc, where } from "firebase/firestore";
import { Clock, Heart, MapPin, Star, Bookmark, Filter, X, ChevronDown, Search } from "lucide-react";
import { db, auth } from "../firebase";
import Header from "./Header";
import PropertyMapWithMarkers from "./PropertyMapWithMarkers";
import { Link } from "react-router-dom";

const BER_RATING_OPTIONS = ["A1", "A2", "A3", "B1", "B2", "B3", "C1", "C2", "C3", "D1", "D2", "E1", "E2", "F", "G"];
const MAX_SUGGESTIONS = 5;
const MAX_SEARCH_HISTORY = 5;
const DEBOUNCE_DELAY = 300;
const INITIAL_FILTERS = {
  saleType: "",
  price: "",
  beds: "",
  homeType: "",
  parkingSpaces: "",
  bathrooms: "",
  mgmtCharges: "",
  builtYear: "",
  areaSize: "",
  berRating: ""
};
const useDropdownState = () => {
  const [dropdowns, setDropdowns] = useState({
    search: false,
    bathroom: false,
    parking: false,
    price: false,
    beds: false,
    mgmtCharges: false,
    builtYear: false,
    areaSize: false,
    berRating: false
  });

  const closeAllDropdowns = useCallback(() => {
    setDropdowns((prev) => Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: false }), {}));
  }, []);

  const toggleDropdown = useCallback((name) => {
    setDropdowns((prev) => ({
      ...Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: false }), {}),
      [name]: !prev[name]
    }));
  }, []);

  return { dropdowns, closeAllDropdowns, toggleDropdown };
};
const useSearchHistory = () => {
  const [searchHistory, setSearchHistory] = useState([]);
  useEffect(() => {
    const savedSearches = JSON.parse(localStorage.getItem("recentSearches")) || [];
    setSearchHistory(savedSearches);
  }, []);
  const saveSearchQuery = useCallback((query) => {
    if (!query.trim()) return;

    setSearchHistory((prevHistory) => {
      const updated = [query.trim(), ...prevHistory.filter((s) => s !== query.trim())].slice(0, MAX_SEARCH_HISTORY);
      localStorage.setItem("recentSearches", JSON.stringify(updated));
      return updated;
    });
  }, []);
  return { searchHistory, saveSearchQuery };
};

const useAuthUser = () => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    const currentUser = auth.currentUser;
    setUser(currentUser);
  }, []);

  return user;
};

const formatPrice = (price) => {
  if (!price) return "";
  const numPrice = Number(price);
  return isNaN(numPrice) ? price : numPrice.toLocaleString("en-US");
};

const extractSearchParams = (input) => {
  const bedsMatch = input.match(/(\d+)\s*(bed|beds|bedroom|bedrooms)/i);
  const bathsMatch = input.match(/(\d+)\s*(bath|baths|bathroom|bathrooms)/i);
  const parkingMatch = input.match(/(\d+)\s*(parking|parkingspaces|parking spaces)/i);

  const beds = bedsMatch ? parseInt(bedsMatch[1]) : null;
  const bathrooms = bathsMatch ? parseInt(bathsMatch[1]) : null;
  const parkingSpaces = parkingMatch ? parseInt(parkingMatch[1]) : null;

  const address = input
    .replace(/(\d+\s*(bed|beds|bedroom|bedrooms))/gi, "")
    .replace(/(\d+\s*(bath|baths|bathroom|bathrooms))/gi, "")
    .replace(/(\d+\s*(parking|parkingspaces|parking spaces))/gi, "")
    .trim();

  return { address, beds, bathrooms, parkingSpaces };
};

// Filter logic
const applyPropertyFilters = (properties, filters, queryText) => {
  return properties.filter((property) => {
    const q = queryText.toLowerCase().trim();
    const textMatch = (() => {
      if (!q) return true;
      const queryWords = q.split(" ").filter(Boolean);
      const fieldText = [property.address, property.eirCode, `${property.bedrooms || ""} bed`, `${property.bathrooms || ""} bath`, `${property.parkingSpaces || ""} parking`].map((val) => val.toString().toLowerCase()).join(" ");

      return queryWords.every((word) => fieldText.includes(word.toLowerCase()));
    })();
    const bedsMatch = !filters.beds || !property.bedrooms || parseInt(property.bedrooms) >= parseInt(filters.beds);
    const priceMatch = !filters.price || !property.salePrice || parseFloat(property.salePrice) >= parseFloat(filters.price);
    const bathroomsMatch = !filters.bathrooms || !property.bathrooms || parseInt(property.bathrooms) >= parseInt(filters.bathrooms);
    const parkingMatch = !filters.parkingSpaces || !property.parkingSpaces || parseInt(property.parkingSpaces) >= parseInt(filters.parkingSpaces);
    const homeTypeMatch = !filters.homeType || !property.propertyType || property.propertyType?.toLowerCase() === filters.homeType.toLowerCase();
    const mgmtChargesMatch = !filters.mgmtCharges || !property.mgmtCharges || parseFloat(property.mgmtCharges || 0) <= parseFloat(filters.mgmtCharges);
    const builtYearMatch = !filters.builtYear || !property.builtYear || parseInt(property.builtYear) >= parseInt(filters.builtYear);
    const areaSizeMatch = !filters.areaSize || !property.areaSize || parseFloat(property.areaSize || 0) >= parseFloat(filters.areaSize);
    const berRatingMatch = (() => {
      if (!filters.berRating || !property.berRating) return true;
      const filterIndex = BER_RATING_OPTIONS.indexOf(filters.berRating);
      const propertyIndex = BER_RATING_OPTIONS.indexOf(property.berRating);
      return propertyIndex <= filterIndex;
    })();
    return textMatch && priceMatch && bedsMatch && homeTypeMatch && bathroomsMatch && parkingMatch && mgmtChargesMatch && builtYearMatch && areaSizeMatch && berRatingMatch;
  });
};

export default function ListingsPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [queryText, setQueryText] = useState("");
  const [properties, setProperties] = useState([]);
  const [filters, setFilters] = useState(INITIAL_FILTERS);
  const [suggestions, setSuggestions] = useState([]);
  const [savedSearches, setSavedSearches] = useState([]);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showMoreFilters, setShowMoreFilters] = useState(false);
  const [activeFilter, setActiveFilter] = useState(null);

  const { searchHistory, saveSearchQuery } = useSearchHistory();
  const { dropdowns, closeAllDropdowns, toggleDropdown } = useDropdownState();
  const user = useAuthUser();
  const filteredProperties = useMemo(() => applyPropertyFilters(properties, filters, queryText), [properties, filters, queryText]);
  const fetchProperties = useCallback(async () => {
    try {
      const typeParam = searchParams.get("type");
      const saleOrRentParam = searchParams.get("saleOrRent");

      const q = query(collection(db, "properties"));
      const snapshot = await getDocs(q);
      let results = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
      if (typeParam === "buy") {
        results = results.filter((p) => p.saleOrRent === true);
      } else if (typeParam === "rent") {
        results = results.filter((p) => p.saleOrRent === false);
      }
      if (saleOrRentParam !== null) {
        const isForSale = saleOrRentParam === "true";
        results = results.filter((p) => p.saleOrRent === isForSale);
      }
      setProperties(results);
    } catch (err) {
      console.error("Error fetching properties:", err);
    }
  }, [searchParams]);

  const fetchSavedSearches = useCallback(async () => {
    if (!user?.email) return;

    try {
      const q = query(collection(db, "saveSearch"), where("email", "==", user.email));
      const snapshot = await getDocs(q);
      const results = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
      setSavedSearches(results);
    } catch (err) {
      console.error("Error fetching saved searches:", err);
    }
  }, [user?.email]);

  const generateSuggestions = useCallback(async (searchText) => {
    if (!searchText.trim()) {
      setSuggestions([]);
      return;
    }

    const allDocs = await getDocs(collection(db, "properties"));
    const matches = [];

    allDocs.forEach((doc) => {
      const data = doc.data();
      const address = data.address?.toLowerCase() || "";
      if (address.includes(searchText.toLowerCase())) {
        const key = `${data.address} ${data.bedrooms || ""} bed ${data.bathrooms || ""} bath ${data.parkingSpaces || 0} parking`;
        if (!matches.includes(key)) {
          matches.push(key);
        }
      }
    });

    setSuggestions(matches.slice(0, MAX_SUGGESTIONS));
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const q = params.get("q") || "";
    setQueryText(q);
  }, [location.search]);

  useEffect(() => {
    const beds = searchParams.get("beds");
    const bathrooms = searchParams.get("bathrooms");
    const parkingSpaces = searchParams.get("parkingSpaces");

    setFilters((prev) => ({
      ...prev,
      beds: beds ? parseInt(beds) : "",
      bathrooms: bathrooms ? parseInt(bathrooms) : "",
      parkingSpaces: parkingSpaces ? parseInt(parkingSpaces) : ""
    }));
  }, [searchParams]);

  useEffect(() => {
    if (location.state?.filters) {
      const { query, price = "", beds = "", homeType = "", bathrooms = "", parkingSpaces = "", saleType = "", mgmtCharges = "", builtYear = "", areaSize = "", berRating = "" } = location.state.filters;

      setQueryText(query || "");
      setFilters({
        price,
        beds,
        homeType,
        bathrooms,
        parkingSpaces,
        saleType,
        mgmtCharges,
        builtYear,
        areaSize,
        berRating
      });

      saveSearchQuery(query || "");
    }
  }, [location.state, saveSearchQuery]);

  useEffect(() => {
    fetchProperties();
  }, [fetchProperties]);

  useEffect(() => {
    if (showSidebar) {
      fetchSavedSearches();
    }
  }, [showSidebar, fetchSavedSearches]);

  useEffect(() => {
    const delayDebounce = setTimeout(() => generateSuggestions(queryText), DEBOUNCE_DELAY);
    return () => clearTimeout(delayDebounce);
  }, [queryText, generateSuggestions]);

  // Event handlers
  const handleSearch = useCallback(() => {
    const trimmed = queryText.trim();
    if (!trimmed) return;
    saveSearchQuery(trimmed);
    navigate(`/search?q=${encodeURIComponent(trimmed)}`);
  }, [queryText, saveSearchQuery, navigate]);

  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === "Enter" && queryText.trim() !== "") {
        const { address, beds, bathrooms, parkingSpaces } = extractSearchParams(queryText);

        const params = new URLSearchParams();
        params.set("q", address);
        if (beds) params.set("beds", beds);
        if (bathrooms) params.set("bathrooms", bathrooms);
        if (parkingSpaces) params.set("parkingSpaces", parkingSpaces);
        params.set("_t", Date.now());

        saveSearchQuery(queryText);
        navigate(`/search?${params.toString()}`);
      }
    },
    [queryText, saveSearchQuery, navigate]
  );

  const handleFilterChange = useCallback((e) => {
    const { name, value } = e.target;
    const numericFields = ["price", "beds", "bathrooms", "parkingSpaces", "mgmtCharges", "builtYear", "areaSize"];

    setFilters((prev) => ({
      ...prev,
      [name]: numericFields.includes(name) ? (value ? parseInt(value) : "") : value
    }));

    setActiveFilter(name);
    setTimeout(() => setActiveFilter(null), 300);
  }, []);

  const clearFilter = useCallback((filterName) => {
    setFilters((prev) => ({ ...prev, [filterName]: "" }));
  }, []);

  const handleSaveSearch = useCallback(async () => {
    const trimmed = queryText.trim();
    if (!user || !trimmed) {
      alert("Search input is required to save search.");
      return;
    }

    try {
      await addDoc(collection(db, "saveSearch"), {
        email: user.email,
        query: trimmed,
        ...filters,
        createdAt: new Date()
      });
      alert("Search saved successfully.");
    } catch (err) {
      console.error("Error saving search:", err);
    }
  }, [user, queryText, filters]);

  const deleteSavedSearch = useCallback(async (searchId, e) => {
    e.stopPropagation();

    try {
      await deleteDoc(doc(db, "saveSearch", searchId));
      setSavedSearches((prev) => prev.filter((search) => search.id !== searchId));
    } catch (err) {
      console.error("Error deleting search:", err);
      alert("Failed to delete search. Please try again.");
    }
  }, []);

  const applySavedSearch = useCallback(
    (searchDoc) => {
      setQueryText(searchDoc.query || "");
      setFilters({
        saleType: searchDoc.saleType || "",
        price: searchDoc.price || "",
        beds: searchDoc.beds || "",
        homeType: searchDoc.homeType || "",
        bathrooms: searchDoc.bathrooms || "",
        parkingSpaces: searchDoc.parkingSpaces || "",
        mgmtCharges: searchDoc.mgmtCharges || "",
        builtYear: searchDoc.builtYear || "",
        areaSize: searchDoc.areaSize || "",
        berRating: searchDoc.berRating || ""
      });

      saveSearchQuery(searchDoc.query || "");
      setShowSidebar(false);
      navigate(`/search?q=${encodeURIComponent(searchDoc.query || "")}`, {
        state: { filters: searchDoc }
      });
    },
    [saveSearchQuery, navigate]
  );

  const handleDropdownClick = useCallback(
    (search) => {
      const { address, beds, bathrooms, parkingSpaces } = extractSearchParams(search);

      const params = new URLSearchParams();
      params.set("q", address);
      if (beds) params.set("beds", beds);
      if (bathrooms) params.set("bathrooms", bathrooms);
      if (parkingSpaces) params.set("parkingSpaces", parkingSpaces);

      setQueryText(search);
      toggleDropdown("search");
      saveSearchQuery(search);
      navigate(`/search?${params.toString()}`);
    },
    [toggleDropdown, saveSearchQuery, navigate]
  );

  return (
    <>
      <Header />
      <div className="pt-20 min-h-screen from-slate-50 via-blue-50 to-indigo-100" style={{ position: "fixed" }}>
        {/* Floating Action Button */}
        <button onClick={() => setShowSidebar(true)} className="fixed top-32 right-6 z-40 group">
          <div className="relative">
            <div className="w-14 h-14 bg-gradient-to-r from-blue-600 to-blue-600 hover:from-blue-700 hover:to-blue-700 text-white rounded-full shadow-xl hover:shadow-2xl transform transition-all duration-300 hover:scale-110 flex items-center justify-center group-hover:rotate-6">
              <Bookmark className="w-6 h-6" />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">{savedSearches.length}</div>
          </div>
          <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">Saved Searches</div>
        </button>

        {/* Enhanced Sidebar */}
        <div className={`fixed inset-0 z-50 transition-opacity duration-300 ${showSidebar ? "opacity-100" : "opacity-0 pointer-events-none"}`}>
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowSidebar(false)} />
          <div className={`absolute top-0 right-0 h-full w-96 bg-white shadow-2xl transform transition-transform duration-500 ease-out ${showSidebar ? "translate-x-0" : "translate-x-full"}`}>
            <div className="h-full flex flex-col">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-blue-600 p-6 text-white">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-xl font-bold">Saved Searches</h2>
                    <p className="text-violet-100 text-sm mt-1">{savedSearches.length} searches saved</p>
                  </div>
                  <button onClick={() => setShowSidebar(false)} className="w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              {user ? (
                <div className="flex-1 overflow-y-auto p-6">
                  {savedSearches.length === 0 ? (
                    <div className="text-center py-16">
                      <Bookmark className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No saved searches yet</p>
                      <p className="text-gray-400 text-sm mt-2">Save your favorite searches to access them quickly</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {savedSearches.map((search, index) => (
                        <div
                          key={search.id}
                          onClick={() => applySavedSearch(search)}
                          className="group bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer relative"
                          style={{ animationDelay: `${index * 100}ms` }}>
                          <button
                            onClick={(e) => deleteSavedSearch(search.id, e)}
                            className="absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-110 shadow-md z-10"
                            title="Delete saved search">
                            <X className="w-3 h-3" />
                          </button>

                          <div className="flex items-start justify-between pr-8">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-2 h-2 bg-violet-500 rounded-full"></div>
                                <p className="font-semibold text-gray-800 group-hover:text-violet-600 transition-colors">{search.query}</p>
                              </div>
                              <div className="flex flex-wrap gap-2 text-xs">
                                {search.beds && <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full">{search.beds}+ Bed</span>}
                                {search.homeType && <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">{search.homeType}</span>}
                                {search.price && <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full">€{search.price}+</span>}
                                {search.bathrooms && <span className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full">{search.bathrooms}+ Bath</span>}
                                {search.parkingSpaces && <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full">{search.parkingSpaces}+ Parking</span>}
                                {search.mgmtCharges && <span className="px-2 py-1 bg-red-100 text-red-700 rounded-full">≤€{search.mgmtCharges} Mgmt</span>}
                                {search.builtYear && <span className="px-2 py-1 bg-teal-100 text-teal-700 rounded-full">{search.builtYear}+ Built</span>}
                                {search.areaSize && <span className="px-2 py-1 bg-pink-100 text-pink-700 rounded-full">{search.areaSize}+ sqft</span>}
                                {search.berRating && <span className="px-2 py-1 bg-cyan-100 text-cyan-700 rounded-full">{search.berRating}+</span>}
                              </div>
                            </div>
                            <Star className="w-4 h-4 text-gray-400 group-hover:text-yellow-500 transition-colors" />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-16">
                  <Bookmark className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Log in to Save Search</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Search Bar */}
        <div className="sticky top-0 z-10 bg-white/90 backdrop-blur-md shadow-lg border-b border-gray-200">
          <div className="px-4 py-3">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200">
                <div className="flex flex-col lg:flex-row">
                  {/* Search Input */}
                  <div className="relative flex-1">
                    <div className="flex items-center">
                      <div className="p-2">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                          <MapPin className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <input
                        type="text"
                        value={queryText}
                        onChange={(e) => setQueryText(e.target.value)}
                        onKeyDown={handleKeyDown}
                        onFocus={() => {
                          if (searchHistory.length > 0) toggleDropdown("search");
                        }}
                        onBlur={() => setTimeout(() => closeAllDropdowns(), 200)}
                        placeholder="Search by address, EIRCODE, bedrooms, or price..."
                        className="flex-1 px-3 py-3 text-sm outline-none bg-transparent placeholder-gray-500 font-medium"
                      />
                      <button onClick={handleSearch} className="m-2 mr-16 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-md transform transition-all duration-200 hover:scale-105 font-semibold text-sm">
                        <Search className="w-4 h-4 mr-1 inline-block" />
                        Search
                      </button>
                    </div>
                    {/* Search Dropdown */}
                    {dropdowns.search && (
                      <div className="absolute z-20 mt-1 w-full bg-white rounded-xl shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                        {suggestions.length > 0 && (
                          <>
                            <div className="px-3 py-2 bg-gray-50 border-b border-gray-100 rounded-t-xl">
                              <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Suggestions</span>
                            </div>
                            {suggestions.map((item, idx) => (
                              <div
                                key={idx}
                                onMouseDown={() => {
                                  const { address, beds, bathrooms, parkingSpaces } = extractSearchParams(item);
                                  const params = new URLSearchParams();
                                  params.set("q", address);
                                  if (beds) params.set("beds", beds);
                                  if (bathrooms) params.set("bathrooms", bathrooms);
                                  if (parkingSpaces) params.set("parkingSpaces", parkingSpaces);
                                  saveSearchQuery(item);
                                  closeAllDropdowns();
                                  navigate(`/search?${params.toString()}`);
                                }}
                                className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-left text-gray-700 border-b border-gray-100 text-sm">
                                <div className="flex items-center">
                                  <Clock className="w-3 h-3 mr-2 text-gray-400" />
                                  <div className="font-medium">{item}</div>
                                </div>
                              </div>
                            ))}
                          </>
                        )}

                        {searchHistory.length > 0 && (
                          <>
                            <div className="px-3 py-2 bg-gray-50 border-b border-gray-100">
                              <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Recent Searches</span>
                            </div>
                            {searchHistory.map((search, idx) => (
                              <div key={idx} className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-left text-gray-700 border-b border-gray-50 last:border-b-0 text-sm" onMouseDown={() => handleDropdownClick(search)}>
                                <div className="flex items-center">
                                  <Clock className="w-3 h-3 mr-2 text-gray-400" />
                                  <div className="font-medium">{search}</div>
                                </div>
                              </div>
                            ))}
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                {/* Compact Filters */}
                <div className="border-t border-gray-100 bg-gradient-to-r from-gray-50 to-white p-3">
                  <div className="flex flex-wrap gap-2 items-center justify-center">
                    <div className="relative">
                      <button onClick={() => toggleDropdown("price")} className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.price ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border-blue-300" : ""}`}>
                        💰 €{filters.price || "Any"}+
                      </button>
                      {dropdowns.price && (
                        <div className="absolute z-50 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 p-3">
                          <label className="block mb-2 text-xs font-semibold text-gray-700">Minimum Price (€)</label>
                          <input type="range" name="price" min="50000" max="1000000" step="50000" value={filters.price || 0} onChange={handleFilterChange} className="w-full accent-blue-600" />
                          <div className="text-center text-xs font-medium text-gray-700 mt-1">€{filters.price || 0}+</div>
                        </div>
                      )}
                    </div>
                    <div className="relative">
                      <button onClick={() => toggleDropdown("beds")} className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.beds ? "bg-gradient-to-r from-green-50 to-teal-50 text-green-700 border-green-300" : ""}`}>
                        🛏️ {filters.beds || "Any"}+ Beds
                      </button>
                      {dropdowns.beds && (
                        <div className="absolute z-50 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 p-3">
                          <label className="block mb-2 text-xs font-semibold text-gray-700">Minimum Bedrooms</label>
                          <input type="range" name="beds" min="0" max="6" step="1" value={filters.beds || 0} onChange={handleFilterChange} className="w-full accent-green-500" />
                          <div className="text-center text-xs font-medium text-gray-700 mt-1">{filters.beds || 0}+</div>
                        </div>
                      )}
                    </div>
                    <div className="relative">
                      <button
                        onClick={() => toggleDropdown("bathroom")}
                        className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.bathrooms ? "bg-gradient-to-r from-indigo-50 to-blue-50 text-indigo-700 border-indigo-300" : ""}`}>
                        🚿 {filters.bathrooms || "Any"}+ Bathrooms
                      </button>
                      {dropdowns.bathroom && (
                        <div className="absolute z-50 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 p-3">
                          <label className="block mb-2 text-xs font-semibold text-gray-700">Select Bathrooms</label>
                          <input type="range" min="0" max="5" step="1" value={filters.bathrooms || 0} name="bathrooms" onChange={handleFilterChange} className="w-full accent-indigo-600" />
                          <div className="text-center text-xs font-medium text-gray-700 mt-1">{filters.bathrooms || 0}+</div>
                        </div>
                      )}
                    </div>
                    <div className="relative">
                      <button
                        onClick={() => toggleDropdown("parking")}
                        className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.parkingSpaces ? "bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-700 border-yellow-300" : ""}`}>
                        🚗 {filters.parkingSpaces || "Any"}+ Parking
                      </button>
                      {dropdowns.parking && (
                        <div className="absolute z-50 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 p-3">
                          <label className="block mb-2 text-xs font-semibold text-gray-700">Select Parking Spaces</label>
                          <input type="range" min="0" max="5" step="1" value={filters.parkingSpaces || 0} name="parkingSpaces" onChange={handleFilterChange} className="w-full accent-yellow-500" />
                          <div className="text-center text-xs font-medium text-gray-700 mt-1">{filters.parkingSpaces || 0}+</div>
                        </div>
                      )}
                    </div>
                    <div className="relative group">
                      <select
                        name="homeType"
                        value={filters.homeType || ""}
                        onChange={handleFilterChange}
                        className={`appearance-none px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold transition-all duration-300 hover:border-purple-300 focus:border-purple-500 focus:outline-none cursor-pointer shadow-sm hover:shadow-md ${
                          activeFilter === "homeType" ? "scale-105 shadow-lg" : ""
                        } ${filters.homeType ? "bg-gradient-to-r from-purple-50 to-pink-50 border-purple-300 text-purple-700" : ""}`}>
                        <option value="">🏠 Any Type</option>
                        <option value="Apartment">🏢 Apartment</option>
                        <option value="House">🏠 House</option>
                        <option value="Townhome">🏘️ Townhome</option>
                        <option value="Villa">🏛️ Villa</option>
                      </select>
                      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-500 pointer-events-none" />
                      {filters.homeType && (
                        <button
                          onClick={() => clearFilter("homeType")}
                          className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs flex items-center justify-center hover:from-red-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-110 shadow-md">
                          ×
                        </button>
                      )}
                    </div>
                    <button
                      onClick={() => setShowMoreFilters(!showMoreFilters)}
                      className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all duration-300 ${showMoreFilters ? "bg-gradient-to-r from-violet-50 to-purple-50 text-violet-700 border-violet-300" : "hover:border-violet-300"}`}>
                      <ChevronDown className={`w-3 h-3 mr-1 inline-block transition-transform duration-300 ${showMoreFilters ? "rotate-180" : ""}`} />
                      More Filters
                    </button>
                    {/* More Filters Dropdown */}
                    <div className={`w-full transition-all duration-500 ease-in-out ${showMoreFilters ? "max-h-96 opacity-100 mt-3 overflow-visible" : "max-h-0 opacity-0 mt-0 overflow-hidden"}`}>
                      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 transform transition-all duration-500 ease-in-out overflow-visible z-10 relative">
                        <div className="flex flex-wrap gap-2 items-center justify-center">
                          <div className="relative z-40">
                            <button
                              onClick={() => toggleDropdown("builtYear")}
                              className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.builtYear ? "bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-700 border-emerald-300" : ""}`}>
                              🏗️ {filters.builtYear ? `${filters.builtYear}+` : "Any Year"}
                            </button>

                            {dropdowns.builtYear && (
                              <div className="absolute z-60 mt-1 w-48 bg-white rounded-lg shadow-2xl border border-gray-200 p-3 left-0 top-full">
                                <label className="block mb-2 text-xs font-semibold text-gray-700">Built After Year</label>
                                <input type="range" name="builtYear" min="1950" max="2024" step="5" value={filters.builtYear || 1950} onChange={handleFilterChange} className="w-full accent-emerald-600" />
                                <div className="text-center text-xs font-medium text-gray-700 mt-1">{filters.builtYear || 1950}+</div>
                              </div>
                            )}
                          </div>
                          <div className="relative z-40">
                            <button
                              onClick={() => toggleDropdown("areaSize")}
                              className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.areaSize ? "bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-300" : ""}`}>
                              📐 {filters.areaSize ? `${filters.areaSize}m²+` : "Any Size"}
                            </button>
                            {dropdowns.areaSize && (
                              <div className="absolute z-60 mt-1 w-48 bg-white rounded-lg shadow-2xl border border-gray-200 p-3 left-0 top-full">
                                <label className="block mb-2 text-xs font-semibold text-gray-700">Minimum Area (m²)</label>
                                <input type="range" name="areaSize" min="20" max="500" step="5" value={filters.areaSize || 50} onChange={handleFilterChange} className="w-full accent-orange-600" />
                                <div className="text-center text-xs font-medium text-gray-700 mt-1">{filters.areaSize || 50}m²+</div>
                              </div>
                            )}
                          </div>
                          <div className="relative z-40">
                            <button
                              onClick={() => toggleDropdown("berRating")}
                              className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.berRating ? "bg-gradient-to-r from-cyan-50 to-blue-50 text-cyan-700 border-cyan-300" : ""}`}>
                              ⚡ {filters.berRating || "Any"} Rating
                            </button>

                            {dropdowns.berRating && (
                              <div className="absolute z-60 mt-1 w-44 bg-white rounded-lg shadow-2xl border border-gray-200 p-3 left-0 top-full">
                                <label className="block mb-2 text-xs font-semibold text-gray-700">Minimum BER Rating</label>
                                <select name="berRating" value={filters.berRating || ""} onChange={handleFilterChange} className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                  <option value="">Any Rating</option>
                                  <option value="A1">A1</option>
                                  <option value="A2">A2</option>
                                  <option value="A3">A3</option>
                                  <option value="B1">B1</option>
                                  <option value="B2">B2</option>
                                  <option value="B3">B3</option>
                                  <option value="C1">C1</option>
                                  <option value="C2">C2</option>
                                  <option value="C3">C3</option>
                                  <option value="D1">D1</option>
                                  <option value="D2">D2</option>
                                  <option value="E1">E1</option>
                                  <option value="E2">E2</option>
                                  <option value="F">F</option>
                                  <option value="G">G</option>
                                </select>
                              </div>
                            )}
                          </div>
                          <div className="relative z-40">
                            <button
                              onClick={() => toggleDropdown("mgmtCharges")}
                              className={`px-3 py-2 bg-white border border-gray-200 rounded-lg text-xs font-semibold shadow-sm hover:shadow-md transition-all ${filters.mgmtCharges ? "bg-gradient-to-r from-rose-50 to-pink-50 text-rose-700 border-rose-300" : ""}`}>
                              📊 {filters.mgmtCharges ? `€${filters.mgmtCharges}+ /mo` : "Any Mgmt Fee"}
                            </button>

                            {dropdowns.mgmtCharges && (
                              <div className="absolute z-60 mt-1 w-48 bg-white rounded-lg shadow-2xl border border-gray-200 p-3 left-0 top-full">
                                <label className="block mb-2 text-xs font-semibold text-gray-700">Maximum Management Charges (€/month)</label>
                                <input type="range" name="mgmtCharges" min="0" max="300" step="10" value={filters.mgmtCharges || 0} onChange={handleFilterChange} className="w-full accent-rose-600" />
                                <div className="text-center text-xs font-medium text-gray-700 mt-1">€{filters.mgmtCharges || 0}+ /month</div>
                              </div>
                            )}
                          </div>
                          <button
                            onClick={() => {
                              setFilters(INITIAL_FILTERS);
                              setShowMoreFilters(false);
                              closeAllDropdowns();
                            }}
                            className="px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 font-semibold text-xs">
                            🗑️ Clear All
                          </button>
                        </div>
                      </div>
                    </div>
                    <button onClick={handleSaveSearch} className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md transform transition-all duration-200 hover:scale-105 font-semibold text-xs">
                      <Heart className="w-3 h-3 mr-1 inline-block" />
                      Save Search
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Layout */}
        <div className="flex flex-col md:flex-row">
          <div className="hidden md:block md:w-1/2 h-screen relative">
            <PropertyMapWithMarkers  filteredProperties={filteredProperties} />
          </div>
          <div className="w-full md:w-1/2 h-screen overflow-y-auto px-6 pt-8 pb-80 sm:pb-70 md:pb-60 lg:pb-60 space-y-6 bg-gradient-to-b from-transparent to-blue-50/30">
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Property Listings</h2>
                <p className="text-gray-600 text-lg mt-1 font-medium">
                  {filteredProperties.length} result
                  {filteredProperties.length !== 1 ? "s" : ""} found
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 pb-8">
              {filteredProperties.map((property, index) => (
                <Link to={`/property/${property.id}`} key={property.id} className="block bg-white rounded-2xl shadow-md overflow-hidden transition-transform transform hover:scale-105 duration-300">
                  <div key={property.id || index} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 border border-gray-100 group" style={{ animationDelay: `${index * 50}ms` }}>
                    <div className="flex flex-col sm:flex-row">
                      <div className="sm:w-2/5 w-full relative overflow-hidden">
                        <img src={property.imageUrls?.[0]} alt="property" className="w-full h-40 sm:h-50 object-cover group-hover:scale-105 transition-transform duration-500" />
                        <div className="absolute top-3 right-3">
                          {/* <button className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center shadow-md hover:bg-red-50 transition-colors duration-200 group/heart">
                            <Heart className="w-4 h-4 text-gray-600 group-hover/heart:text-red-500 transition-colors" />
                          </button> */}
                        </div>
                      </div>
                      <div className="sm:w-3/5 w-full p-4 flex flex-col justify-between">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-xl font-bold text-blue-600">€{formatPrice(property.salePrice)}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${property.saleOrRent ? "bg-red-100 text-red-700" : "bg-orange-100 text-orange-700"}`}>{property.saleOrRent ? "For Sale" : "For Rent"}</span>
                          </div>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {property.bedrooms && <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">🛏️ {property.bedrooms} beds</span>}
                            {property.bathrooms && <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">🚿 {property.bathrooms} bathrooms</span>}
                            {property.areaSize && <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">📐 {property.areaSize}m²</span>}
                          </div>
                          <div className="space-y-1">
                            <p className="text-gray-700 text-sm font-medium flex items-center gap-1">
                              <MapPin className="w-3 h-3 text-gray-500" />
                              {property.address}
                            </p>
                            <p className="text-gray-500 text-xs">
                              <strong>EIRCODE:</strong> {property.eirCode}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }
      `}</style>
    </>
  );
}
