import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { db } from "../firebase";
import { doc, getDoc, collection, getDocs, query, where } from "firebase/firestore";
import { MapPin, Bed, Bath, Car, Home, Star, FileText, Award, Heart, Share2, Calculator, Calendar, Phone, Mail, Layout, Video, ChevronLeft, ChevronRight, X, Camera, TrendingUp } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import Header from "./Header";
import { FaWhatsapp } from "react-icons/fa";
import { Loader } from "@googlemaps/js-api-loader";
import { School, Hospital } from "lucide-react"; // or replace with another icon you prefer
import SimilarPropertyCarousel from "./SimilarPropertyCarousel";
import NearByPropertyCarousel from "./NearByPropertyCarousel";

export default function PropertyDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [property, setProperty] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isLayoutModalOpen, setIsLayoutModalOpen] = useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [nearbySchools, setNearbySchools] = useState([]);
  const [nearbyHospitals, setNearbyHospitals] = useState([]);
  const [nearbyGroceryStores, setNearbyGroceryStores] = useState([]);
  const [commuteAddress, setCommuteAddress] = useState("");
  const [commuteInfo, setCommuteInfo] = useState(null);
  const [nearestAirport, setNearestAirport] = useState(null);
  const [favouriteCommutes, setFavouriteCommutes] = useState([]);
  // New state for agent profile
  const [agentProfile, setAgentProfile] = useState(null);
  const [agentLoading, setAgentLoading] = useState(false);
  const [nearestBus, setNearestBus] = useState(null);
  const [nearestLuas, setNearestLuas] = useState(null);
  const [nearestDart, setNearestDart] = useState(null);
  const [nearestMetro, setNearestMetro] = useState(null);
  const [nearestTram, setNearestTram] = useState(null);
  const [nearestTrain, setNearestTrain] = useState(null);
  // Function to fetch agent profile
  const fetchAgentProfile = async (agentEmail) => {
    if (!agentEmail) return;

    try {
      setAgentLoading(true);
      const profileRef = collection(db, "profiles");
      const q = query(profileRef, where("email", "==", agentEmail));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const agentDoc = querySnapshot.docs[0];
        const agentData = { id: agentDoc.id, ...agentDoc.data() };
        setAgentProfile(agentData);
      } else {
        console.warn("No agent profile found for email:", agentEmail);
        setAgentProfile(null);
      }
    } catch (error) {
      console.error("Error fetching agent profile:", error);
      setAgentProfile(null);
    } finally {
      setAgentLoading(false);
    }
  };
  useEffect(() => {
    const fetchProperty = async () => {
      try {
        const docRef = doc(db, "properties", id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const propertyData = { id: docSnap.id, ...docSnap.data() };
          setProperty(propertyData);

          if (propertyData.agentEmail) {
            fetchAgentProfile(propertyData.agentEmail);
          }

          const loader = new Loader({
            apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
            libraries: ["places"],
          });

          loader.load().then((google) => {
            const service = new google.maps.places.PlacesService(document.createElement("div"));
            const distanceService = new google.maps.DistanceMatrixService();

            const fetchAndSort = (query, type, setter) => {
              service.textSearch({ query, type }, (results, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && results.length > 0) {
                  const destinations = results.map((r) => r.formatted_address || r.name);

                  distanceService.getDistanceMatrix(
                    {
                      origins: [propertyData.address],
                      destinations,
                      travelMode: google.maps.TravelMode.TRANSIT,
                    },
                    (response, status) => {
                      if (status === "OK") {
                        const distances = response.rows[0].elements;

                        const enriched = results.map((place, i) => ({
                          ...place,
                          distance: distances[i]?.distance?.value || Infinity,
                          distanceText: distances[i]?.distance?.text || "Unknown",
                        }));

                        const sorted = enriched.sort((a, b) => a.distance - b.distance);

                        setter(sorted.slice(0, 3));
                      } else {
                        console.warn("DistanceMatrix failed:", status);
                        setter(results.slice(0, 3));
                      }
                    }
                  );
                } else {
                  console.warn(`PlacesService textSearch failed for ${query}:`, status);
                }
              });
            };

            fetchAndSort(`schools near ${propertyData.address}`, "school", setNearbySchools);
            fetchAndSort(`hospitals near ${propertyData.address}`, "hospital", setNearbyHospitals);
            fetchAndSort(`grocery stores near ${propertyData.address}`, "supermarket", setNearbyGroceryStores);
            fetchAndSort(`airport near ${propertyData.address}`, "airport", (results) => {
              if (results.length > 0) {
                setNearestAirport(results[0]);
              }
            });

            const searchTransportByType = (transportType, query, setter) => {
              service.textSearch({ query, type: transportType }, (results, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && results.length > 0) {
                  const destinations = results.map((r) => r.formatted_address || r.name);

                  distanceService.getDistanceMatrix(
                    {
                      origins: [propertyData.address],
                      destinations,
                      travelMode: google.maps.TravelMode.TRANSIT,
                    },
                    (response, status) => {
                      if (status === "OK") {
                        const distances = response.rows[0].elements;

                        const enriched = results.map((place, i) => ({
                          ...place,
                          transportType,
                          distance: distances[i]?.distance?.value || Infinity,
                          distanceText: distances[i]?.distance?.text || "Unknown",
                        }));

                        const sorted = enriched.sort((a, b) => a.distance - b.distance);
                        if (sorted.length > 0) {
                          setter(sorted[0]); // Set the nearest one
                        }
                      } else {
                        console.warn(`DistanceMatrix failed for ${transportType}:`, status);
                      }
                    }
                  );
                } else {
                  console.warn(`${transportType} search failed:`, status);
                }
              });
            };

            // Search for different transport types
            searchTransportByType("Bus", `bus station near ${propertyData.address}`, setNearestBus);
            searchTransportByType("Luas", `luas station near ${propertyData.address}`, setNearestLuas);
            searchTransportByType("DART", `dart station near ${propertyData.address}`, setNearestDart);
            searchTransportByType("Metro", `metro station near ${propertyData.address}`, setNearestMetro);
            searchTransportByType("Tram", `tram stop near ${propertyData.address}`, setNearestTram);
            searchTransportByType("Train", `train station near ${propertyData.address}`, setNearestTrain);


            // Favourite commutes
            const favouritePlaces = [
              {
                label: "Work - Aviva Insurance",
                address: "Aviva Insurance Ireland, Cherrywood Business Park Dublin 18",
              },
              {
                label: "Club - Leinster Cricket Club",
                address: "Leinster Cricket Club Rathmines Dublin 6",
              },
            ];

            distanceService.getDistanceMatrix(
              {
                origins: [propertyData.address],
                destinations: favouritePlaces.map((place) => place.address),
                travelMode: google.maps.TravelMode.DRIVING,
              },
              (response, status) => {
                if (status === "OK") {
                  const distances = response.rows[0].elements;

                  const enrichedCommutes = favouritePlaces.map((place, i) => ({
                    ...place,
                    distanceText: distances[i]?.distance?.text || "Unknown",
                    durationText: distances[i]?.duration?.text || "Unknown",
                  }));

                  setFavouriteCommutes(enrichedCommutes);
                } else {
                  console.warn("DistanceMatrix for favourite commutes failed:", status);
                }
              }
            );
          });
        } else {
          console.warn("No such property document");
        }
      } catch (error) {
        console.error("Error fetching property:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProperty();
  }, [id]);

  const geocodeAddress = (address) => {
    return new Promise((resolve, reject) => {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address }, (results, status) => {
        if (status === "OK" && results[0]) {
          resolve(results[0].geometry.location);
        } else {
          reject(`Geocode failed for ${address}: ${status}`);
        }
      });
    });
  };

  const calculateCommute = async () => {
    try {
      const loader = new Loader({
        apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
        libraries: ["places"],
      });

      await loader.load();

      const origin = await geocodeAddress(property.address);
      const destination = await geocodeAddress(commuteAddress);

      const service = new google.maps.DistanceMatrixService();

      service.getDistanceMatrix(
        {
          origins: [origin],
          destinations: [destination],
          travelMode: google.maps.TravelMode.DRIVING,
          unitSystem: google.maps.UnitSystem.METRIC,
        },
        (response, status) => {
          if (status === "OK") {
            const result = response.rows[0].elements[0];
            if (result.status === "OK") {
              setCommuteInfo({
                distance: result.distance.text,
                duration: result.duration.text,
              });
            } else {
              console.warn("Route calculation failed:", result.status);
              setCommuteInfo(null);
            }
          } else {
            console.error("DistanceMatrixService failed:", status);
          }
        }
      );
    } catch (error) {
      console.error("Commute calculation error:", error);
      setCommuteInfo(null);
    }
  };

  const nextImage = () => {
    if (property?.imageUrls?.length > 1) {
      setSelectedImageIndex((prev) => (prev === property.imageUrls.length - 1 ? 0 : prev + 1));
    }
  };

  const prevImage = () => {
    if (property?.imageUrls?.length > 1) {
      setSelectedImageIndex((prev) => (prev === 0 ? property.imageUrls.length - 1 : prev - 1));
    }
  };

  // Generate price chart data
  const generatePriceChartData = () => {
    if (!property) return [];

    const today = new Date();
    const oneYearAgo = new Date(today);
    oneYearAgo.setFullYear(today.getFullYear() - 1);

    // Create sample data points - in real app, this would come from your database
    const data = [];

    // Add historical price point if available
    if (property.price_in_euro && property.date_of_sale) {
      const saleDate = new Date(property.date_of_sale);
      data.push({
        date: saleDate.toLocaleDateString("en-GB", {
          month: "short",
          year: "numeric",
        }),
        price: property.price_in_euro,
        fullDate: saleDate,
      });
    }

    // Add current price point
    data.push({
      date: today.toLocaleDateString("en-GB", {
        month: "short",
        year: "numeric",
      }),
      price: property.salePrice || property.price_in_euro || 0,
      fullDate: today,
    });

    // Sort by date
    data.sort((a, b) => a.fullDate - b.fullDate);

    return data;
  };

  const priceChartData = generatePriceChartData();

  if (loading) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gray-50 pt-16 sm:pt-20">
          <div className="max-w-7xl mx-auto p-3 sm:p-4 lg:p-6">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-32 sm:w-48 mb-4 sm:mb-6"></div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                <div className="lg:col-span-2">
                  <div className="h-64 sm:h-80 lg:h-96 xl:h-[500px] bg-gray-200 rounded-xl mb-4"></div>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mb-6">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="h-16 sm:h-20 bg-gray-200 rounded-lg"></div>
                    ))}
                  </div>
                </div>
                <div className="space-y-4 sm:space-y-6">
                  <div className="h-48 sm:h-64 bg-gray-200 rounded-xl"></div>
                  <div className="h-32 sm:h-48 bg-gray-200 rounded-xl"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
  function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }
  if (!property) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gray-50 pt-16 sm:pt-20 flex items-center justify-center px-4">
          <div className="text-center">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Property Not Found</h2>
            <p className="text-gray-600 mb-4 text-sm sm:text-base">The property you're looking for doesn't exist.</p>
            <button onClick={() => navigate("/")} className="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-sm sm:text-base">
              Back to Home
            </button>
          </div>
        </div>
      </>
    );
  }

  const images = property.imageUrls || [];
  const selectedImage = images[selectedImageIndex] || images[0];

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 pt-16 sm:pt-20">
        <div className="max-w-7xl mx-auto p-3 sm:p-4 lg:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {/* Image Gallery */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                {/* Main Image */}
                <div className="relative h-64 sm:h-80 lg:h-96 xl:h-[500px] group">
                  <img src={selectedImage} alt="Property" className="w-full h-full object-cover cursor-pointer" onClick={() => setIsImageModalOpen(true)} />

                  {/* Image Navigation */}
                  {images.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-1.5 sm:p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <ChevronLeft size={18} sm:size={20} />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-1.5 sm:p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <ChevronRight size={18} sm:size={20} />
                      </button>
                    </>
                  )}

                  {/* Badges */}
                  <div className="absolute top-2 sm:top-4 left-2 sm:left-4 flex gap-1 sm:gap-2">
                    {property.isFeatured && (
                      <div className="bg-yellow-500 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium flex items-center gap-1">
                        <Star size={12} sm:size={14} fill="currentColor" />
                        <span className="hidden sm:inline">Featured</span>
                      </div>
                    )}
                    {property.saleOrRent ? (
                      <div className="bg-green-500 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">For Sale</div>
                    ) : (
                      <div className="bg-blue-500 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">For Rent</div>
                    )}
                  </div>

                  <div className="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-black/75 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">
                    {selectedImageIndex + 1} / {images.length}
                  </div>

                  {/* Action Buttons - All in one line */}
                  <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 flex gap-2">
                    {/* View All Photos Button */}
                    <button
                      onClick={() => setIsImageModalOpen(true)}
                      className="bg-white/90 hover:bg-white text-gray-800 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg font-medium flex items-center gap-1 sm:gap-2 transition-colors text-xs sm:text-sm"
                    >
                      <Camera size={14} sm:size={16} />
                      <span className="hidden sm:inline">View All {images.length} Photos</span>
                      <span className="sm:hidden">{images.length} Photos</span>
                    </button>

                    {/* Layout Button */}
                    {property.layoutImageUrl && (
                      <button
                        onClick={() => setIsLayoutModalOpen(true)}
                        className="bg-white/90 hover:bg-white text-gray-800 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg font-medium flex items-center gap-1 sm:gap-2 transition-colors text-xs sm:text-sm"
                      >
                        <Layout size={14} sm:size={16} />
                        <span className="hidden sm:inline">Layout</span>
                        <span className="sm:hidden">Layout</span>
                      </button>
                    )}

                    {/* Video Button */}
                    {property.videoUrl && (
                      <button
                        onClick={() => setIsVideoModalOpen(true)}
                        className="bg-white/90 hover:bg-white text-gray-800 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg font-medium flex items-center gap-1 sm:gap-2 transition-colors text-xs sm:text-sm"
                      >
                        <Video size={14} sm:size={16} />
                        <span className="hidden sm:inline">Video</span>
                        <span className="sm:hidden">Video</span>
                      </button>
                    )}
                  </div>
                </div>

                {/* Image Thumbnails */}
                {images.length > 1 && (
                  <div className="p-3 sm:p-4 bg-gray-50">
                    <div className="flex gap-2 overflow-x-auto pb-2">
                      {images.slice(0, 10).map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`relative h-12 sm:h-16 w-16 sm:w-24 flex-shrink-0 rounded-lg overflow-hidden transition-all ${
                            selectedImageIndex === index ? "ring-2 ring-blue-500 ring-offset-2" : "hover:opacity-80"
                          }`}
                        >
                          <img src={image} alt={`Property ${index + 1}`} className="w-full h-full object-cover" />
                        </button>
                      ))}
                      {images.length > 10 && (
                        <button
                          onClick={() => setIsImageModalOpen(true)}
                          className="h-12 sm:h-16 w-16 sm:w-24 flex-shrink-0 rounded-lg bg-gray-200 hover:bg-gray-300 transition-colors flex items-center justify-center text-gray-600 font-medium text-xs sm:text-sm"
                        >
                          +{images.length - 10}
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Property Description */}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                <h2 className="text-lg sm:text-2xl font-bold text-gray-900 mb-4">About This Property</h2>
                {property.usp && <p className="text-gray-700 leading-relaxed mb-6 text-sm sm:text-base">{property.usp}</p>}
                {nearestAirport && (
                  <div>
                    <h2 className="text-lg sm:text-xl font-bold text-gray-900">Nearest Airport:</h2>
                    <div className="text-gray-800 font-semibold">{nearestAirport.name}</div>
                    <div className="text-sm text-gray-600">{nearestAirport.formatted_address}</div>
                    <div className="text-sm text-blue-600">Distance: {nearestAirport.distanceText}</div>
                    <br />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Property Features</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <Bed size={16} className="text-gray-600 sm:size-[18px]" />
                        <span className="text-gray-700 text-sm sm:text-base">{property.bedrooms} Bedrooms</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Bath size={16} className="text-gray-600 sm:size-[18px]" />
                        <span className="text-gray-700 text-sm sm:text-base">{property.bathrooms} Bathrooms</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Car size={16} className="text-gray-600 sm:size-[18px]" />
                        <span className="text-gray-700 text-sm sm:text-base">{property.parkingSpaces} Parking Spaces</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Home size={16} className="text-gray-600 sm:size-[18px]" />
                        <span className="text-gray-700 text-sm sm:text-base">
                          {property.areaSize} m<sup>2</sup>
                        </span>
                      </div>

                      {/* USP Component styled to match */}
                      <div className="flex items-center gap-3">
                        <Star size={16} className="text-gray-600 sm:size-[18px]" />
                        <span className="text-gray-700 text-sm sm:text-base font-medium">{property.usp}</span>
                      </div>

                      {/* Description Component styled to match */}
                      <div className="flex items-start gap-3">
                        <FileText size={16} className="text-gray-600 sm:size-[18px] mt-0.5" />
                        <span className="text-gray-700 text-sm sm:text-base leading-relaxed">{property.description}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Property Details</h3>
                    <div className="space-y-2">
                      <div className="flex">
                        <span className="text-gray-600 text-sm sm:text-base">Property Type:</span>
                        <span className="font-medium text-gray-900 text-sm sm:text-base pl-2">{property.propertyType}</span>
                      </div>
                      <div className="flex">
                        <span className="text-gray-600 text-sm sm:text-base">BER Rating:</span>
                        <span className="font-medium text-gray-900 flex items-center gap-1 text-sm sm:text-base  pl-2">
                          <Award size={14} sm:size={16} className="text-green-600" />
                          {property.berRating}
                        </span>
                      </div>
                      <div className="flex">
                        <span className="text-gray-600 text-sm sm:text-base">Built Year:</span>
                        <span className="font-medium text-gray-900 text-sm sm:text-base  pl-2">{property.mgmt}</span>
                      </div>
                      <div className="flex">
                        <span className="text-gray-600 text-sm sm:text-base">Management Charges:</span>
                        <span className="font-medium text-gray-900 text-sm sm:text-base  pl-2">{property.builtYear}</span>
                      </div>
                      {property.eirCode && (
                        <div className="flex ">
                          <span className="text-gray-600 text-sm sm:text-base">EIR Code:</span>
                          <span className="font-medium text-gray-900 text-sm sm:text-base pl-2">{property.eirCode}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {property.address && (
                <a
                  href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(property.address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white/90 mt-4 shadow-sm hover:bg-white text-gray-800 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg font-medium flex items-center justify-center gap-1 transition-colors text-xs sm:text-sm"
                >
                  <MapPin size={14} />
                  <span>View on Map</span>
                </a>
              )}

              {favouriteCommutes.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4">Commute to Favourites</h2>
                  <ul className="space-y-2">
                    {favouriteCommutes.map((place, idx) => (
                      <li key={idx} className="text-sm sm:text-base text-gray-700">
                        <strong>{place.label}:</strong> {place.distanceText} ({place.durationText} 🚗)
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Agent Card */}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">Listed By</h3>
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 sm:w-12 h-10 sm:h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <Home size={18} sm:size={20} className="text-gray-600" />
                  </div>
                  <div>
                    {/* <div className="font-semibold text-gray-900 text-sm sm:text-base">{agentProfile?.email}</div> */}
                    <div className="text-xs sm:text-sm text-gray-600"> Real Estate Professional':-{agentProfile?.companyName}</div>
                    <div className="text-xs sm:text-sm text-gray-600">License Number:- {agentProfile?.psrLicenseNumber}</div>
                    <div className="text-xs sm:text-sm text-gray-600"> {agentProfile?.companyWebsite}</div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {/* <button className="flex-1 bg-blue-600 text-white py-2 px-3 sm:px-4 rounded-lg text-xs sm:text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-1 sm:gap-2">
                    <Phone size={14} sm:size={16} />
                    Call
                  </button> */}
                  <a
                    className="flex-1 border border-gray-300 text-gray-700 py-2 px-3 sm:px-4 rounded-lg text-xs sm:text-sm font-medium hover:bg-gray-50 transition-colors flex items-center justify-center gap-1 sm:gap-2"
                    href={`mailto:${property.agentEmail}`}
                  >
                    <Mail size={14} />
                    {property.agentEmail}
                  </a>
                </div>
              </div>

              {/* Price Chart */}
              {priceChartData.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <TrendingUp size={20} className="text-blue-600" />
                    Price History
                  </h2>
                  <div className="h-64 sm:h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={priceChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis tickFormatter={(value) => `€${(value / 1000).toFixed(0)}K`} />
                        <Tooltip formatter={(value) => [`€${value.toLocaleString()}`, "Price"]} labelFormatter={(label) => `Date: ${label}`} />
                        <Line type="monotone" dataKey="price" stroke="#2563eb" strokeWidth={3} dot={{ fill: "#2563eb", strokeWidth: 2, r: 6 }} />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Nearby Schools */}
              {nearbySchools.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <School size={20} className="text-blue-600" />
                    Nearby Schools
                  </h2>
                  <ul className="space-y-3">
                    {nearbySchools.map((school, idx) => (
                      <li key={idx} className="border p-3 rounded-lg shadow-sm">
                        <div className="font-medium text-gray-900">{school.name}</div>
                        <div className="text-gray-600 text-sm">{school.formatted_address}</div>
                        <p>Distance: {school.distanceText}</p>
                        {school.rating && <div className="text-sm text-yellow-600">⭐ {school.rating}</div>}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Nearby Hospitals */}
              {nearbyHospitals.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <Hospital size={20} className="text-red-600" />
                    Nearby Hospitals
                  </h2>
                  <ul className="space-y-3">
                    {nearbyHospitals.map((hospital, idx) => (
                      <li key={idx} className="border p-3 rounded-lg shadow-sm">
                        <div className="font-medium text-gray-900">{hospital.name}</div>
                        <div className="text-gray-600 text-sm">{hospital.formatted_address}</div>
                        <p>Distance: {hospital.distanceText}</p>
                        {hospital.rating && <div className="text-sm text-yellow-600">⭐ {hospital.rating}</div>}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {nearbyGroceryStores.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">🛒 Nearby Grocery Stores</h2>
                  <ul className="space-y-3">
                    {nearbyGroceryStores.map((store, idx) => (
                      <li key={idx} className="border p-3 rounded-lg shadow-sm">
                        <div className="font-medium text-gray-900">{store.name}</div>
                        <div className="text-gray-600 text-sm">{store.formatted_address}</div>
                        <p>Distance: {store.distanceText}</p>
                        {store.rating && <div className="text-sm text-yellow-600">⭐ {store.rating}</div>}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {/* Combined Transport Section */}
              {(nearestBus || nearestLuas || nearestDart || nearestTrain || nearestMetro || nearestTram) && (
                <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">🚇 Public Transport</h2>
                  <ul className="space-y-3">
                    {[
                      nearestBus && { ...nearestBus, type: "Bus" },
                      nearestLuas && { ...nearestLuas, type: "Luas" },
                      nearestDart && { ...nearestDart, type: "DART" },
                      nearestTrain && { ...nearestTrain, type: "Train" }
                      // nearestMetro && { ...nearestMetro, type: "Metro" }
                      // nearestTram && { ...nearestTram, type: "Tram" }
                    ]
                      .filter(Boolean)
                      .sort((a, b) => a.distance - b.distance)
                      .slice(0, 4)
                      .map((transport, index) => (
                        <li key={index} className="border p-3 rounded-lg shadow-sm">
                          <div className="font-medium text-gray-900">{transport.name} - Nearest {transport.type}</div>
                          <div className="text-gray-600 text-sm">{transport.formatted_address}</div>
                          <p>Distance: {transport.distanceText}</p>
                        </li>
                      ))}
                  </ul>
                </div>
              )}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mt-4 sm:mt-6">
                <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">🚗 Commute To Favourites</h2>
                <div className="flex flex-col sm:flex-row gap-3">
                  <input
                    type="text"
                    value={commuteAddress}
                    onChange={(e) => setCommuteAddress(e.target.value)}
                    placeholder="Commute To Your Favorite from your GAFF"
                    className="border border-gray-300 rounded-lg px-4 py-2 w-full"
                  />
                  <button onClick={calculateCommute} className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Calculate
                  </button>
                </div>

                {commuteInfo && (
                  <div className="mt-4 text-sm sm:text-base text-gray-700">
                    <p>
                      <strong>Distance:</strong> {commuteInfo.distance}
                    </p>
                    <p>
                      <strong>Estimated Time:</strong>🚗 {commuteInfo.duration}
                    </p>
                  </div>
                )}
              </div>
            </div>
            {/* Sidebar */}
            <div className="space-y-4 sm:space-y-6">
              {/* Price and Location */}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 lg:sticky lg:top-20">
                <div className="mb-4 sm:mb-6">
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">€{formatPrice(property.salePrice)}</div>
                  <div className="flex items-start gap-2 text-gray-600">
                    <MapPin size={16} sm:size={18} className="mt-1 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{property.address}</span>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-gray-900">{property.bedrooms}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Bedrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-gray-900">{property.bathrooms}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Bathrooms</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-gray-900">{property.parkingSpaces}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Parking Space</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-gray-900">
                      {property.areaSize}m<sup>2</sup>
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600">Area Size</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 mb-4 sm:mb-6">
                  <div className="flex items-center gap-2 sm:gap-3">
                    {/* <button
                      onClick={() => setIsLiked(!isLiked)}
                      className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-sm sm:text-base ${
                        isLiked ? "bg-red-50 text-red-600 border border-red-200" : "bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100"
                      }`}
                    >
                      <Heart size={16} sm:size={18} fill={isLiked ? "currentColor" : "none"} />
                      <span className="hidden sm:inline">{isLiked ? "Saved" : "Save"}</span>
                    </button>
                    <button className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-gray-50 text-gray-700 border border-gray-200 rounded-lg font-medium hover:bg-gray-100 transition-colors text-sm sm:text-base">
                      <Share2 size={16} sm:size={18} />
                      <span className="hidden sm:inline">Share</span>
                    </button> */}
                  </div>
                  {agentProfile?.phoneFull && (
                    <button className="w-full bg-blue-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center gap-2 text-sm sm:text-base">
                      <Phone size={16} />
                      {agentProfile.phoneFull}
                    </button>
                  )}
                  {agentProfile?.whatsappFull && (
                    <a
                      className="w-full bg-green-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 text-sm sm:text-base"
                      href={`https://wa.me/${agentProfile.whatsappFull}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FaWhatsapp size={16} color="" />
                      WhatsApp Agent
                    </a>
                  )}
                  <button
                    onClick={() => navigate(`/scheduleAppointMent/${property.id}`)}
                    className="w-full bg-blue-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center gap-2 text-sm sm:text-base"
                  >
                    <Calendar size={16} />
                    Schedule Viewing
                  </button>
                  <button className="w-full border border-gray-300 text-gray-700 py-2.5 sm:py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center gap-2 text-sm sm:text-base">
                    <Calculator size={16} sm:size={18} />
                    Mortgage Calculator
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <SimilarPropertyCarousel currentAddress={property.address} currentBedrooms={property.bedrooms} currentBathrooms={property.bathrooms} />
      {property.address && <NearByPropertyCarousel currentAddress={property.address} />}

      {isImageModalOpen && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            <button onClick={() => setIsImageModalOpen(false)} className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full z-10">
              <X size={24} />
            </button>

            <img src={selectedImage} alt="Property" className="max-w-full max-h-full object-contain" />

            {images.length > 1 && (
              <>
                <button onClick={prevImage} className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white p-3 rounded-full">
                  <ChevronLeft size={24} />
                </button>
                <button onClick={nextImage} className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 text-white p-3 rounded-full">
                  <ChevronRight size={24} />
                </button>
              </>
            )}

            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full">
              {selectedImageIndex + 1} of {images.length}
            </div>
          </div>
        </div>
      )}
      {isLayoutModalOpen && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            <button onClick={() => setIsLayoutModalOpen(false)} className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full z-10">
              <X size={24} />
            </button>

            {/* Zoom Controls */}
            <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
              <button
                onClick={() => {
                  const img = document.querySelector("#layout-image");
                  if (img) {
                    const currentScale = parseFloat(img.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
                    const newScale = Math.min(currentScale * 1.2, 5);
                    img.style.transform = `scale(${newScale})`;
                  }
                }}
                className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full"
              >
                <span className="text-xl font-bold">+</span>
              </button>
              <button
                onClick={() => {
                  const img = document.querySelector("#layout-image");
                  if (img) {
                    const currentScale = parseFloat(img.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
                    const newScale = Math.max(currentScale / 1.2, 0.5);
                    img.style.transform = `scale(${newScale})`;
                  }
                }}
                className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full"
              >
                <span className="text-xl font-bold">-</span>
              </button>
              <button
                onClick={() => {
                  const img = document.querySelector("#layout-image");
                  if (img) {
                    img.style.transform = "scale(1)";
                  }
                }}
                className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full text-xs"
              >
                Reset
              </button>
            </div>

            <div className="w-full h-full flex items-center justify-center overflow-hidden">
              <img
                id="layout-image"
                src={property.layoutImageUrl}
                alt="Property Layout"
                className="max-w-full max-h-full object-contain transition-transform duration-200 cursor-grab select-none"
                style={{ transform: "scale(1)" }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  let isDragging = false;
                  let startX = e.clientX;
                  let startY = e.clientY;
                  let startTranslateX = 0;
                  let startTranslateY = 0;

                  // Get current transform values
                  const currentTransform = e.target.style.transform;
                  const scaleMatch = currentTransform.match(/scale\(([^)]+)\)/);
                  const translateXMatch = currentTransform.match(/translateX\(([^)]+)px\)/);
                  const translateYMatch = currentTransform.match(/translateY\(([^)]+)px\)/);

                  const currentScale = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
                  startTranslateX = translateXMatch ? parseFloat(translateXMatch[1]) : 0;
                  startTranslateY = translateYMatch ? parseFloat(translateYMatch[1]) : 0;

                  const handleMouseMove = (e) => {
                    if (!isDragging) return;

                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;

                    const newTranslateX = startTranslateX + deltaX;
                    const newTranslateY = startTranslateY + deltaY;

                    e.target.style.transform = `scale(${currentScale}) translateX(${newTranslateX}px) translateY(${newTranslateY}px)`;
                  };

                  const handleMouseUp = () => {
                    isDragging = false;
                    document.removeEventListener("mousemove", handleMouseMove);
                    document.removeEventListener("mouseup", handleMouseUp);
                    e.target.style.cursor = "move";
                  };

                  isDragging = true;
                  e.target.style.cursor = "grabbing";
                  document.addEventListener("mousemove", handleMouseMove);
                  document.addEventListener("mouseup", handleMouseUp);
                }}
                onWheel={(e) => {
                  e.preventDefault();
                  const img = e.target;
                  const currentScale = parseFloat(img.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
                  const delta = e.deltaY > 0 ? 0.9 : 1.1;
                  const newScale = Math.min(Math.max(currentScale * delta, 0.5), 5);

                  const currentTransform = img.style.transform;
                  const translateX = parseFloat(currentTransform.match(/translateX\(([^)]+)px\)/)?.[1] || 0);
                  const translateY = parseFloat(currentTransform.match(/translateY\(([^)]+)px\)/)?.[1] || 0);

                  img.style.transform = `scale(${newScale}) translateX(${translateX}px) translateY(${translateY}px)`;
                }}
              />
            </div>
          </div>
        </div>
      )}
      {isVideoModalOpen && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            <button onClick={() => setIsVideoModalOpen(false)} className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full z-10">
              <X size={24} />
            </button>

            <div className="max-w-6xl max-h-full w-full">
              <div className="bg-black rounded-b-lg">
                <video src={property.videoUrl} controls className="w-full h-auto max-h-[80vh] object-contain" autoPlay>
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
