import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow, useJsApiLoader } from "@react-google-maps/api";

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

const mapContainerStyle = {
  width: "100%",
  height: "100vh",
};

const defaultCenter = {
  lat: 53.3498,
  lng: -6.2603,
};

export default function PropertyMapWithMarkers({ filteredProperties = [] }) {
  const [markerPositions, setMarkerPositions] = useState({});
  const [activeMarker, setActiveMarker] = useState(null);
  const [mapCenter, setMapCenter] = useState(defaultCenter);
  const [mapZoom, setMapZoom] = useState(12);

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
  });

  useEffect(() => {
    const fetchAllGeocodes = async () => {
      if (filteredProperties.length === 0) {
        setMarkerPositions({});
        setMapCenter(defaultCenter);
        setMapZoom(12);
        return;
      }

      const geocoded = {};
      const validPositions = [];

      for (const property of filteredProperties) {
        const address = property.address;
        if (!address) continue;

        try {
          const res = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${GOOGLE_MAPS_API_KEY}`);
          const data = await res.json();
          const loc = data?.results?.[0]?.geometry?.location;
          if (loc) {
            geocoded[property.id] = loc;
            validPositions.push(loc);
          }
        } catch (err) {
          console.error("Geocoding error:", address, err);
        }
      }

      setMarkerPositions(geocoded);

      // Calculate center and zoom based on marker positions
      if (validPositions.length > 0) {
        if (validPositions.length === 1) {
          // Single property - center on it
          setMapCenter(validPositions[0]);
          setMapZoom(15);
        } else {
          // Multiple properties - calculate bounds
          const bounds = {
            north: Math.max(...validPositions.map(pos => pos.lat)),
            south: Math.min(...validPositions.map(pos => pos.lat)),
            east: Math.max(...validPositions.map(pos => pos.lng)),
            west: Math.min(...validPositions.map(pos => pos.lng))
          };

          // Calculate center
          const center = {
            lat: (bounds.north + bounds.south) / 2,
            lng: (bounds.east + bounds.west) / 2
          };
          setMapCenter(center);

          // Calculate appropriate zoom level based on bounds
          const latDiff = bounds.north - bounds.south;
          const lngDiff = bounds.east - bounds.west;
          const maxDiff = Math.max(latDiff, lngDiff);

          let zoom = 12;
          if (maxDiff < 0.01) zoom = 15;
          else if (maxDiff < 0.05) zoom = 13;
          else if (maxDiff < 0.1) zoom = 11;
          else if (maxDiff < 0.5) zoom = 9;
          else zoom = 7;

          setMapZoom(zoom);
        }
      }
    };

    fetchAllGeocodes();
  }, [filteredProperties]);

  if (!isLoaded) return <div>Loading map...</div>;

  function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price;
    return numPrice.toLocaleString("en-US");
  }

  function formatPriceShort(priceStr) {
    const price = parseInt(priceStr, 10);
    if (isNaN(price)) return "";

    if (price >= 1_000_000) {
      return `${(price / 1_000_000).toFixed(1).replace(/\.0$/, "")}M`;
    }
    if (price >= 1_000) {
      return `${Math.round(price / 1_000)}K`;
    }
    return price.toString();
  }

  return (
    <div className="w-full h-screen">
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        zoom={mapZoom}
        center={mapCenter}
        key={`${mapCenter.lat}-${mapCenter.lng}-${mapZoom}`} // Force re-render when center/zoom changes
      >
        {filteredProperties.map((property) => {
          const position = markerPositions[property.id];
          if (!position) return null;

          return (
            <React.Fragment key={property.id}>
              <Marker
                position={position}
                label={{
                  text: `€${formatPriceShort(property.salePrice)}`,
                  color: "black",
                  fontSize: "12px",
                  fontWeight: "bold",
                }}
                icon={{
                  url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                  scaledSize: window.google && window.google.maps ? new window.google.maps.Size(32, 32) : undefined,
                  labelOrigin:
                    window.google && window.google.maps
                      ? new window.google.maps.Point(16, -10)
                      : undefined,
                }}
                onClick={() => setActiveMarker(property.id)}
              />

              {activeMarker === property.id && (
                <InfoWindow position={position} onCloseClick={() => setActiveMarker(null)}>
                  <div className="text-sm font-medium text-gray-700 max-w-xs">
                    {property.address} <br />€{formatPrice(property.salePrice)}
                  </div>
                </InfoWindow>
              )}
            </React.Fragment>
          );
        })}
      </GoogleMap>
    </div>
  );
}
