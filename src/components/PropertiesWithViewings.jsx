import React, { useEffect, useState } from "react";
import { db } from "../firebase";
import { collection, query, where, getDocs } from "firebase/firestore";
import { useAuth } from "../context/AuthProvider";
import { useNavigate } from "react-router-dom";
import { FaCalendarAlt, FaEye, FaHome } from "react-icons/fa";
import { Euro } from "lucide-react";

export default function PropertiesWithViewings() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [propertiesWithViewings, setPropertiesWithViewings] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPropertiesWithViewings = async () => {
      if (!user?.email) return;

      try {
        // First, get all properties by this agent
        const propertiesQuery = query(
          collection(db, "properties"),
          where("agentEmail", "==", user.email)
        );
        const propertiesSnapshot = await getDocs(propertiesQuery);
        const properties = propertiesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Then, get all viewings for this agent
        const viewingsQuery = query(
          collection(db, "viewings"),
          where("agentEmail", "==", user.email)
        );
        const viewingsSnapshot = await getDocs(viewingsQuery);
        const viewings = viewingsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Group viewings by property
        const viewingsByProperty = viewings.reduce((acc, viewing) => {
          if (!acc[viewing.propertyId]) {
            acc[viewing.propertyId] = [];
          }
          acc[viewing.propertyId].push(viewing);
          return acc;
        }, {});

        // Filter properties that have viewings and add viewing count and latest viewings
        const propertiesWithViewingsData = properties
          .filter(property => viewingsByProperty[property.id])
          .map(property => {
            const propertyViewings = viewingsByProperty[property.id];
            // Sort viewings by scheduled date (ascending order) and get the latest 3
            const sortedViewings = propertyViewings.sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate));
            const latestThreeViewings = sortedViewings.slice(-3);

            return {
              ...property,
              viewingsCount: propertyViewings.length,
              latestThreeViewings: latestThreeViewings
            };
          });

        setPropertiesWithViewings(propertiesWithViewingsData);
      } catch (err) {
        console.error("Failed to fetch properties with viewings:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPropertiesWithViewings();
  }, [user]);

  const handleViewPropertyViewings = (propertyId) => {
    navigate(`/mylistings/viewings/${propertyId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg font-medium">Loading properties with viewings...</p>
        </div>
      </div>
    );
  }

  if (propertiesWithViewings.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white-50 to-white-100 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-gray-200 rounded-full flex items-center justify-center">
            <FaCalendarAlt className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">No Viewings Scheduled</h3>
          <p className="text-gray-600">None of your properties have scheduled viewings yet.</p>
        </div>
      </div>
    );
  }
function formatPrice(price) {
    if (!price) return "";
    const numPrice = Number(price);
    if (isNaN(numPrice)) return price; // fallback if price isn't a valid number
    return numPrice.toLocaleString("en-US");
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-white-50 to-white-100">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Properties with Viewings</h1>
          <p className="text-gray-600 text-lg">Manage your properties that have scheduled viewings</p>
          <div className="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded mt-4"></div>
        </div>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
          {propertiesWithViewings.map((property) => (
            <div
              key={property.id}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden border border-gray-100 cursor-pointer"
              onClick={() => handleViewPropertyViewings(property.id)}
            >
              <div className="p-6">
                {/* Property Header */}
                <div className="mb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 leading-tight">
                        {property.address}
                      </h3>
                      {property.eirCode && (
                        <span className="inline-block bg-indigo-100 text-indigo-800 text-sm font-medium px-3 py-1 rounded-full">
                          {property.eirCode}
                        </span>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                        <FaHome className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="flex items-center text-blue-600 font-bold text-xl mb-3">
                    <Euro size={20} className="mr-1" />
                    {formatPrice(property.salePrice)}
                  </div>
                </div>

                {/* Viewing Statistics */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-white-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <FaCalendarAlt className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Total Viewings</p>
                      <p className="text-gray-800 font-semibold">
                        {property.viewingsCount} Viewing{property.viewingsCount !== 1 ? 's' : ''} Scheduled
                      </p>
                    </div>
                  </div>
                </div>

                {/* Latest 3 Viewings */}
                <div className="border-t border-gray-100 pt-4 mb-4">
                  <div className="mb-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Latest Viewings</h4>
                  </div>
                  <div className="space-y-2">
                    {property.latestThreeViewings.map((viewing, index) => (
                      <div key={viewing.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                        <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-purple-600 text-xs font-semibold">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-800">{viewing.userEmail}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(viewing.scheduledDate).toLocaleString("en-IN", {
                              dateStyle: "short",
                              timeStyle: "short",
                              timeZone: "Asia/Kolkata",
                            })}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex justify-center">
                  <button className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 font-medium shadow-md hover:shadow-lg">
                    <FaEye className="mr-2" />
                    View All Viewings
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
