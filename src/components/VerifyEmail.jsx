// VerifyEmail.jsx
import { useEffect, useState } from "react";
import { auth } from "../firebase";
import { useNavigate } from "react-router-dom";
import Header from "./Header";
export default function VerifyEmail() {
  const navigate = useNavigate();
  const [verified, setVerified] = useState(false);
  const [checking, setChecking] = useState(false);

  const handleCheckVerification = async () => {
    if (auth.currentUser) {
      setChecking(true);
      await auth.currentUser.reload(); // 🔁 Reload latest user info
      if (auth.currentUser.emailVerified) {
        setVerified(true);
        navigate("/"); // ✅ Redirect to homepage
      } else {
        alert("Email not verified yet. Please check your inbox.");
      }
      setChecking(false);
    }
  };

  return (
    <>
    <Header />
    <div className="max-w-md mx-auto mt-10 text-center p-6 bg-white shadow rounded">
      <h1 className="text-2xl font-bold mb-4">Verify your Email</h1>
      <p className="mb-6">
        A verification link has been sent to your email. Please verify and then click the button below.
      </p>

      <button
        onClick={handleCheckVerification}
        disabled={checking}
        className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
      >
        {checking ? "Checking..." : "I've Verified My Email"}
      </button>
    </div>
    </>
  );
}
