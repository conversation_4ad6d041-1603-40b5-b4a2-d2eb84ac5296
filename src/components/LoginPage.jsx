import { FcGoogle } from "react-icons/fc";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaFacebookF } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { auth, provider } from "../firebase";
import {
  signInWithEmailAndPassword,
  sendEmailVerification,
  signInWithPopup,
  FacebookAuthProvider,
  fetchSignInMethodsForEmail,
} from "firebase/auth";
import {
  collection,
  query,
  where,
  getDocs,
  setDoc,
  doc,
} from "firebase/firestore";
import { db } from "../firebase";

export default function LoginPage() {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleClick = () => {
    navigate("/createAccount");
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = result.user;

      if (!user.emailVerified) {
        alert("Please verify your email before logging in.");
        alert("Verification email sent again");
        await sendEmailVerification(user);
        await auth.signOut();
        return;
      }

      navigate("/");
    } catch (error) {
      if (error.code === "auth/user-not-found") {
        alert("No account found with this email.");
      } else if (error.code === "auth/wrong-password") {
        alert("Incorrect password.");
      } else if (error.code === "auth/invalid-credential") {
        alert(
          "This email may be registered with Google. Please try Google Sign-In."
        );
      } else {
        alert("Login failed: " + error.message);
      }
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      const profilesRef = collection(db, "profiles");
      const q = query(profilesRef, where("email", "==", user.email));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        await setDoc(doc(profilesRef), {
          email: user.email,
          address: "",
          area: "",
          favoritePlace: "",
          phoneNumber: "",
          bio: "",
          website: "",
        });
      }

      navigate("/");
    } catch (error) {
      alert("Google Sign-In failed. Please try again.");
    }
  };

  const handleFacebookSignIn = async () => {
    const facebookProvider = new FacebookAuthProvider();
    try {
      const result = await signInWithPopup(auth, facebookProvider);
      const user = result.user;
      const profilesRef = collection(db, "profiles");
      const q = query(profilesRef, where("email", "==", user.email));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        await setDoc(doc(profilesRef), {
          email: user.email,
          address: "",
          area: "",
          favoritePlace: "",
          phoneNumber: "",
          bio: "",
          website: "",
        });
      }

      navigate("/");
    } catch (error) {
      console.error("Facebook Sign-In Error:", error.code, error.message);
      alert("Facebook Sign-In failed. Please try again.");
    }
  };

  const handleAppleSignIn = async () => {
    const simulatedEmail = prompt("Enter your Apple email to simulate login:");
    if (!simulatedEmail) return;

    try {
      const signInMethods = await fetchSignInMethodsForEmail(
        auth,
        simulatedEmail
      );
      if (!signInMethods.includes("apple.com")) {
        alert("No account found with this Apple login.");
        return;
      }

      alert("Apple login simulated. Redirecting...");
      navigate("/");
    } catch (error) {
      alert("Apple Sign-In failed. Please try again.");
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Form */}
      <div className="w-full md:w-1/2 lg:w-1/3 flex items-center justify-center px-6 bg-white">
        <div className="max-w-sm w-full space-y-6">
          {/* Logo */}
          <div className="text-left">
            <img
              className="h-6 mb-6 cursor-pointer"
              src="https://www.GAFFstatic.com/static/images/logo-GAFF-full.png"
              alt="Find My Gaff"
              onClick={() => navigate("/")}
            />
            <h2 className="text-2xl font-semibold text-gray-900">Sign in</h2>
          </div>

          {/* Email Form */}
          <form onSubmit={handleLogin} className="space-y-4">
            <input
              type="email"
              placeholder="Email Address*"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="block w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 text-sm"
            />
            <input
              type="password"
              placeholder="Password*"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="block w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600 text-sm"
            />
            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md text-sm"
            >
              Continue
            </button>
          </form>

          {/* Create Account Link */}
          <div className="text-sm text-gray-600 text-center">
            New to GAFF?{" "}
            <button
              onClick={handleClick}
              className="text-blue-600 font-medium hover:underline"
            >
              Create account
            </button>
          </div>

          {/* Divider */}
          <div className="flex items-center gap-2 text-gray-400 text-sm">
            <div className="flex-1 h-px bg-gray-300" />
            OR
            <div className="flex-1 h-px bg-gray-300" />
          </div>

          {/* Social Logins */}
          <div className="space-y-3">
            <button
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FcGoogle className="w-5 h-5" />
              Continue with Google
            </button>
            <button
              onClick={handleAppleSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FaApple className="w-5 h-5" />
              Continue with Apple
            </button>
            <button
              onClick={handleFacebookSignIn}
              className="w-full flex items-center justify-center gap-3 border border-gray-300 py-3 px-6 rounded-lg hover:bg-gray-50 text-sm"
            >
              <FaFacebookF className="w-5 h-5 text-blue-600" />
              Continue with Facebook
            </button>
          </div>

          {/* Terms */}
          <p className="text-xs text-gray-500 text-center">
            By submitting, I accept GAFF's{" "}
            <a href="#" className="text-blue-600 hover:underline">
              terms of use
            </a>
          </p>
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="hidden md:block md:w-1/2 lg:w-2/3">
        <img
          className="h-full w-full object-cover"
          src="https://images.unsplash.com/photo-1507089947368-19c1da9775ae?auto=format&fit=crop&q=80&w=1600"
          alt="House"
        />
      </div>
    </div>
  );
}
