import Header from "./Header";
import { FiSearch } from "react-icons/fi";
import { ChevronLeft, ChevronRight, Heart, Clock } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import { auth, db } from "../firebase";
import { onAuthStateChanged } from "firebase/auth";
import { collection, getDocs, query, where, orderBy, limit } from "firebase/firestore";
import AOS from "aos";
import "aos/dist/aos.css";
import BuyAbilityAction from "./main/BuyAbilityAction";
import Promos from "./main/promos";
import { Link } from "react-router-dom";
import Footer from "./Footer";
import HomeCarousel from "./HomeCarousel";

export default function Home() {
  const navigate = useNavigate();
  const [query2, setQuery] = useState("");
  const [user, setUser] = useState(null);
  const [searchHistory, setSearchHistory] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [activeTab, setActiveTab] = useState("buy");
  const [lastSavedSearch, setLastSavedSearch] = useState(null);
  // Add this new state
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!query2.trim()) {
        setSuggestions([]);
        return;
      }

      const allDocs = await getDocs(collection(db, "properties"));
      const matches = [];

      allDocs.forEach((doc) => {
        const data = doc.data();
        const address = data.address?.toLowerCase() || "";
        if (address.includes(query2.toLowerCase())) {
          const key = `${data.address} ${data.bedrooms || ""} bed ${data.bathrooms || ""} bath ${data.parkingSpaces || 0} parking`;
          if (!matches.includes(key)) {
            matches.push(key);
          }
        }
      });

      setSuggestions(matches.slice(0, 5)); // show top 5
    };

    const delayDebounce = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(delayDebounce);
  }, [query2]);
  const backgroundImages = ["/images/1.jfif", "/images/2.jfif", "/images/3.jfif", "/images/4.jfif", "/images/5.jfif"];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % backgroundImages.length);
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [backgroundImages.length]);
  useEffect(() => {
    if (!user) return;

    const fetchLastSavedSearch = async () => {
      try {
        const q = query(collection(db, "saveSearch"), where("email", "==", user.email), orderBy("createdAt", "desc"), limit(1));
        const querySnapshot = await getDocs(q);
        if (!querySnapshot.empty) {
          const lastDoc = querySnapshot.docs[0].data();
          setLastSavedSearch(lastDoc);
        }
      } catch (error) {
        console.error("Error fetching last saved search:", error);
      }
    };

    fetchLastSavedSearch();
  }, [user]);

  const inputRef = useRef(null);
  useEffect(() => {
    AOS.init({
      once: true,
    });
  }, []);
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    // Load search history from localStorage
    const savedSearches = JSON.parse(localStorage.getItem("recentSearches")) || [];
    setSearchHistory(savedSearches);
  }, [user]);

  const saveSearchQuery = (newQuery) => {
    if (!newQuery.trim()) return;
    const trimmedQuery = newQuery.trim();
    let updatedHistory = [...searchHistory.filter((q) => q !== trimmedQuery)];
    updatedHistory.unshift(trimmedQuery);
    updatedHistory = updatedHistory.slice(0, 5); // limit to last 5

    localStorage.setItem("recentSearches", JSON.stringify(updatedHistory));
    setSearchHistory(updatedHistory);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && query2.trim() !== "") {
      const saleOrRent = activeTab === "buy" ? "true" : "false";
      navigate(`/search?q=${encodeURIComponent(query2)}&saleOrRent=${saleOrRent}`);
    }
  };

  const handleDropdownClick = (search) => {
    const parts = search.split(" ");
    const addressParts = [];
    let beds = null;
    let bathrooms = null;
    let parkingSpaces = null;

    for (let i = 0; i < parts.length; i++) {
      const word = parts[i].toLowerCase();
      const prev = parts[i - 1];

      if (word === "bed" && prev && !isNaN(prev)) {
        beds = parseInt(prev);
      } else if (word === "bath" && prev && !isNaN(prev)) {
        bathrooms = parseInt(prev);
      } else if ((word === "parking" || word === "parkingspaces") && prev && !isNaN(prev)) {
        parkingSpaces = parseInt(prev);
      } else if (!["bed", "bath", "parking", "parkingspaces"].includes(word) && isNaN(word)) {
        addressParts.push(parts[i]);
      }
    }

    const address = addressParts.join(" ").trim();
    const saleOrRent = activeTab === "buy" ? "true" : "false";

    const params = new URLSearchParams();
    params.set("q", address);
    params.set("saleOrRent", saleOrRent);
    if (beds) params.set("beds", beds);
    if (bathrooms) params.set("bathrooms", bathrooms);
    if (parkingSpaces) params.set("parkingSpaces", parkingSpaces);

    setQuery(search);
    setShowDropdown(false);
    saveSearchQuery(search);
    navigate(`/search?${params.toString()}`);
  };

  const handleFocus = () => {
    const savedSearches = JSON.parse(localStorage.getItem("recentSearches")) || [];
    setSearchHistory(savedSearches);
    if (savedSearches.length > 0) {
      setShowDropdown(true);
    }
  };

  const handleBlur = () => {
    setTimeout(() => setShowDropdown(false), 200);
  };

  const handleLastSearchClick = () => {
    navigate(`/search?q=${encodeURIComponent(lastSavedSearch.query)}`, {
      state: {
        filters: lastSavedSearch,
      },
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="pt-14">
        <section className="relative h-screen bg-cover bg-center">
          {backgroundImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 bg-cover bg-center transition-opacity duration-1000 ease-in-out ${index === currentImageIndex ? "opacity-100" : "opacity-0"}`}
              style={{
                backgroundImage: `url('${image}')`,
              }}
            />
          ))}
          <div className="absolute inset-0 bg-black/40" />
          <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-4 ">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight mb-2 text-center">Find your way home</h1>

            <div className="mt-12 w-full max-w-4xl">
              {/* Search Container with White Background */}
              <div className="bg-white rounded-2xl shadow-2xl p-8 mx-4">
                {/* Navigation Tabs */}
                <div className="flex justify-center mb-8">
                  <div className="flex space-x-0 bg-gray-50 rounded-lg p-1">
                    <button
                      onClick={() => setActiveTab("buy")}
                      className={`px-6 py-2 font-semibold transition-all duration-200 rounded-md ${
                        activeTab === "buy" ? "text-blue-600 bg-white shadow-sm border-b-2 border-blue-600" : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                      }`}
                    >
                      Buy
                    </button>
                    <button
                      onClick={() => setActiveTab("rent")}
                      className={`px-6 py-2 font-medium transition-all duration-200 rounded-md ${
                        activeTab === "rent" ? "text-blue-600 bg-white shadow-sm border-b-2 border-blue-600" : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                      }`}
                    >
                      Rent
                    </button>
                  </div>
                </div>

                {/* Search Bar */}
                <div className="relative">
                  <div className="flex items-center bg-gray-50 rounded-xl border-2 border-gray-200 focus-within:border-blue-500 focus-within:bg-white transition-all duration-200 px-4 py-4">
                    <FiSearch className="text-gray-400 text-xl mr-3" />
                    <input
                      ref={inputRef}
                      type="text"
                      value={query2}
                      onChange={(e) => setQuery(e.target.value)}
                      onKeyDown={handleKeyDown}
                      onFocus={handleFocus}
                      onBlur={handleBlur}
                      placeholder="County, City, Town or Area"
                      className="flex-grow text-gray-700 text-lg focus:outline-none bg-transparent placeholder-gray-400"
                    />
                  </div>

                  {/* Last Search Display */}
                  {searchHistory.length > 0 && !showDropdown && (
                    <div>
                      {lastSavedSearch && (
                        <div
                          onClick={handleLastSearchClick}
                          className="flex items-center justify-between mt-3 w-full border rounded-lg bg-gray-50 text-gray-600 text-sm cursor-pointer hover:bg-gray-100 transition-colors"
                        >
                          <div className="flex items-center p-3">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div>
                                <div className="text-sm text-gray-600">Last search:</div>
                                <div className="font-medium text-gray-900 capitalize">{lastSavedSearch.query}</div>
                                <div className="text-sm text-gray-500">
                                  {lastSavedSearch?.beds && `${lastSavedSearch.beds} Beds`}
                                  {lastSavedSearch?.bathrooms && ` ${lastSavedSearch.beds} Bathroom`}
                                  {lastSavedSearch?.parkingSpaces && ` ${lastSavedSearch.parkingSpaces} Parking Spaces`}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="pr-3">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Dropdown */}
                  {showDropdown && (
                    <div className="absolute z-20 mt-2 w-full bg-white rounded-xl shadow-xl border border-gray-200 max-h-80 overflow-y-auto">
                      {/* Suggestions */}
                      {suggestions.length > 0 && (
                        <>
                          <div className="px-4 py-3 bg-gray-50 border-b border-gray-100 rounded-t-xl">
                            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Suggestions</span>
                          </div>
                          {suggestions.map((item, idx) => (
                            <div
                              key={idx}
                              onMouseDown={() => {
                                const parts = item.split(" ");
                                const addressParts = [];
                                let beds = null;
                                let bathrooms = null;
                                let parkingSpaces = null;

                                for (let i = 0; i < parts.length; i++) {
                                  const word = parts[i].toLowerCase();
                                  const prev = parts[i - 1];

                                  if (word === "bed" && prev && !isNaN(prev)) {
                                    beds = parseInt(prev);
                                  } else if (word === "bath" && prev && !isNaN(prev)) {
                                    bathrooms = parseInt(prev);
                                  } else if ((word === "parking" || word === "parkingspaces") && prev && !isNaN(prev)) {
                                    parkingSpaces = parseInt(prev);
                                  } else if (!["bed", "bath", "parking", "parkingspaces"].includes(word) && isNaN(word)) {
                                    addressParts.push(parts[i]);
                                  }
                                }

                                const address = addressParts.join(" ").trim();
                                const saleOrRent = activeTab === "buy" ? "true" : "false";

                                const params = new URLSearchParams();
                                params.set("q", address);
                                params.set("saleOrRent", saleOrRent);
                                if (beds) params.set("beds", beds);
                                if (bathrooms) params.set("bathrooms", bathrooms);
                                if (parkingSpaces) params.set("parkingSpaces", parkingSpaces);

                                saveSearchQuery(item);
                                setShowDropdown(false);
                                navigate(`/search?${params.toString()}`);
                              }}
                              className="px-4 py-3 hover:bg-blue-50 cursor-pointer text-left text-gray-700 border-b border-gray-100"
                            >
                              <div className="font-medium">{item}</div>
                            </div>
                          ))}
                        </>
                      )}

                      {/* Recent searches fallback */}
                      {searchHistory.length > 0 && (
                        <>
                          <div className="px-4 py-3 bg-gray-50 border-b border-gray-100">
                            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Recent Searches</span>
                          </div>
                          {searchHistory.map((search, idx) => (
                            <div
                              key={idx}
                              className="px-4 py-3 hover:bg-blue-50 cursor-pointer text-left text-gray-700 border-b border-gray-50 last:border-b-0"
                              onMouseDown={() => handleDropdownClick(search)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Clock className="w-4 h-4 mr-3 text-gray-400" />
                                  <div>
                                    <div className="font-medium">{search}</div>
                                    <div className="text-sm text-gray-500">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} - Best Match</div>
                                  </div>
                                </div>
                                <ChevronRight className="w-4 h-4 text-gray-400" />
                              </div>
                            </div>
                          ))}
                        </>
                      )}
                    </div>
                  )}
                </div>

                {/* Bottom Description */}
                <div className="mt-6 text-center">
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">
                    {activeTab === "buy" && "Houses and Apartments for Sale on Find My GAFF"}
                    {activeTab === "rent" && "Houses and Apartments for Rent on Find My GAFF"}
                    {activeTab === "share" && "Room Shares and House Shares on Find My GAFF"}
                    {activeTab === "sold" && "Sold Properties and Price History on Find My GAFF"}
                  </h3>
                  <p className="text-gray-600">
                    {activeTab === "buy" && "Find your next home on Ireland's favourite property site"}
                    {activeTab === "rent" && "Discover rental properties across Ireland"}
                    {activeTab === "share" && "Find your perfect shared accommodation"}
                    {activeTab === "sold" && "View recent sales and market trends"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {user ? (
          <HomeCarousel />
        ) : (
          <section className="py-20 px-4 bg-white" data-aos="fade-up" data-aos-duration="1000" data-aos-offset="200">
            <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div className="space-y-4 md:pr-8 text-center md:text-left">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Get home recommendations</h2>
                <p className="text-gray-600 text-lg">Sign in for a more personalized experience.</p>
                <button onClick={() => navigate("/login")} className="px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition text-base font-medium">
                  Sign in
                </button>
              </div>
              <div className="flex justify-center md:justify-end">
                <img
                  src="https://www.zillowstatic.com/s3/web-platform/sub-apps/hops-homepage/hops-homepage.prod.master.9611654.1560ab49/web/1d9d5bce566c85fa242cb21ad3292cb8.webp"
                  alt="Recommended Homes"
                  className="w-full max-w-md md:max-w-lg rounded-xl shadow-xl"
                />
              </div>
            </div>
          </section>
        )}

        <BuyAbilityAction />
        <Promos />
        <Footer />
      </main>
    </div>
  );
}
