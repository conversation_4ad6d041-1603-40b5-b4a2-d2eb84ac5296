# React Vite Project

This is a React application built with [Vite](https://vitejs.dev/), which provides a fast development environment and optimized production build.

---

## 🛠️ Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v16 or newer recommended): [Download Node.js](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn**

To verify installation, run:

```bash
node -v
npm -v
# or
yarn -v



git clone https://github.com/your-username/GAFF-Website.git
cd GAFF-Website

npm install
# or
yarn install


npm run dev
# or
yarn dev
