{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@googlemaps/js-api-loader": "^1.16.10", "@react-google-maps/api": "^2.20.7", "@tailwindcss/vite": "^4.1.10", "aos": "^2.3.4", "firebase": "^12.0.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-firebase-hooks": "^5.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "recharts": "^3.0.2", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/aos": "^3.0.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "firebase-tools": "^14.11.0", "globals": "^16.0.0", "prettier": "^3.6.2", "vite": "^7.0.5"}}